const gulp = require('gulp')
const combiner = require('stream-combiner2') // 组合流可以找到报错的地方
const browserSync = require('browser-sync').create()
const less = require('gulp-less')
const clean = require('gulp-clean')
// const ejs = require("ejs-json");
const ejs = require('gulp-ejs')
const concat = require('gulp-concat')

const { watch, series, parallel } = require('gulp')
const LessAutoprefix = require('less-plugin-autoprefix') // less自动化插件

const config = require('./config.js') // 引入配置文件
const data = require('./src/data/1-1-1.json') // 引入数据文件

var autoprefix = new LessAutoprefix(config.autoprefixer) // 自定义浏览器前缀
//reload
function reload(cb) {
  browserSync.reload()
  cb()
}

//default
function develop() {
  browserSync.init(config.browsersync.development)

  //less watcher
  watch(config.src.less.files, series(compile_less, reload))
  // ejs watcher
  let ejs_watcher = watch(config.src.ejs.files)
  ejs_watcher.on('change', function (path, stats) {
    console.log(`File ${path} was changed`)
    return (
      gulp
        .src(path, { base: config.src.url })
        //  .pipe(ejs({ data: path + '/data' }))
        .pipe(ejs({ data: data }))
        .pipe(gulp.dest(config.build.url))
        .pipe(browserSync.reload({ stream: true }))
    )
  })
  ejs_watcher.on('add', function (path, stats) {
    console.log(`File ${path} was add`)
    return gulp
      .src(path, { base: config.src.url })
      .pipe(ejs({ data: data }))
      .pipe(gulp.dest(config.build.url))
      .pipe(browserSync.reload({ stream: true }))
  })
  ejs_watcher.on('unlink', function (path, stats) {
    console.log(`File ${path} was removed, please rebuild gulp`)
  })

  //pages/ejs watchers
  watch(config.src.pages_ejs.files, series(compile_ejs, reload))

  //modules watchers
  watch(config.src.modules.tpl, series(compile_ejs, reload))

  //js watchers
  watch(config.src.js.allFiles, series(compile_js, reload))

  //img watchers
  watch(config.src.img.files, series(move_images, reload))

  //icon watchers
  watch(config.src.icon.files, series(move_icons, reload))

  //plugins watchers
  watch(config.src.plugins.files, series(move_plugins, reload))

  //config.js watchers
  watch(config.src.config.files, series(compile_config_js, reload))
}

//build
//------------------------start

function clean_build() {
  return gulp.src(config.build.url + '/*').pipe(clean({ force: true }))
}

// 编译less
function compile_less() {
  return combiner
    .obj([
      gulp.src(config.src.less.main),
      less({
        plugins: [autoprefix],
      }),
      // concat('style.css'),
      gulp.dest(config.build.css.url),
    ])
    .on('error', console.error.bind(console))
}

// 编译html
function compile_ejs() {
  return combiner
    .obj([
      gulp.src(config.src.ejs.files),
      ejs({ data: data }),
      gulp.dest(config.build.url),
    ])
    .on('error', console.error.bind(console))
}

function compile_js(cb) {
  // 过滤掉allFiles属性，因为它包含了所有JS文件
  const jsKeys = Object.keys(config.src.js).filter((key) => key !== 'allFiles')

  // 创建一个任务数组，用于存储所有编译任务
  const tasks = jsKeys
    .map((key) => {
      // 跳过没有文件路径的配置项
      if (!config.src.js[key] || !config.src.js[key].length) return null

      // 为每个配置项创建一个文件名
      const fileName = `${key}.js`

      return function (done) {
        return combiner
          .obj([
            gulp.src(config.src.js[key]),
            concat(fileName),
            gulp.dest(config.build.js.url),
          ])
          .on('error', function (err) {
            console.error(err)
            done(err)
          })
          .on('end', function () {
            done()
          })
      }
    })
    .filter(Boolean) // 过滤掉null值

  // 如果没有任务，直接完成回调
  if (tasks.length === 0) {
    cb()
    return
  }

  // 使用series执行所有任务，并在完成后调用回调
  const combinedTask = series(...tasks)
  return combinedTask(cb)
}

// 编译页面指针的configjs
function compile_config_js() {
  return combiner
    .obj([gulp.src(config.src.config.files), gulp.dest(config.build.js.url)])
    .on('error', console.error.bind(console))
}

// 移动plugins
function move_plugins() {
  return combiner
    .obj([
      gulp.src(config.src.plugins.files),
      gulp.dest(config.build.plugins.url),
    ])
    .on('error', console.error.bind(console))
}

// 移动fonts文件
function move_icons() {
  return combiner
    .obj([gulp.src(config.src.icon.files), gulp.dest(config.build.icon.url)])
    .on('error', console.error.bind(console))
}

function move_images() {
  return combiner
    .obj([
      gulp.src(config.src.img.files, { encoding: false }),
      gulp.dest(config.build.img.url),
    ])
    .on('error', console.error.bind(console))
}

//pack
//---start
function clean_pack() {
  return combiner
    .obj([gulp.src(config.dist.url + '/*'), clean({ force: true })])
    .on('error', console.error.bind(console))
}

function pack_html() {
  return combiner
    .obj([
      gulp.src(config.build.url + '/**/**/*.html'),
      gulp.dest(config.dist.url),
    ])
    .on('error', console.error.bind(console))
}

function pack_js() {
  return combiner
    .obj([gulp.src(config.build.js.files), gulp.dest(config.dist.js.url)])
    .on('error', console.error.bind(console))
}

function pack_images() {
  return combiner
    .obj([
      gulp.src(config.build.img.files, { encoding: false }),
      gulp.dest(config.dist.img.url),
    ])
    .on('error', console.error.bind(console))
}

function pack_css() {
  return combiner
    .obj([gulp.src(config.build.css.files), gulp.dest(config.dist.css.url)])
    .on('error', console.error.bind(console))
}

function pack_plugins() {
  return combiner
    .obj([
      gulp.src(config.build.plugins.files),
      gulp.dest(config.dist.plugins.url),
    ])
    .on('error', console.error.bind(console))
}

function pack_icon() {
  return combiner
    .obj([gulp.src(config.build.icon.files), gulp.dest(config.dist.icon.url)])
    .on('error', console.error.bind(console))
}

//main task
//---start
exports.default = series(develop)

exports.build = series(
  clean_build,
  parallel(
    compile_less,
    compile_ejs,
    compile_js,
    compile_config_js,
    move_icons,
    move_plugins,
    move_images
  )
)

exports.pack = series(
  clean_pack,
  parallel(pack_html, pack_js, pack_images, pack_css, pack_plugins, pack_icon)
)

function del_build_pack() {
  return gulp
    .src([config.build.clean, config.dist.clean])
    .pipe(clean({ force: true }))
}
exports.build_pack_del = series(del_build_pack)
