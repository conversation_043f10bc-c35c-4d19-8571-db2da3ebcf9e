// inputs 组件
//
// input, padding, border and color for body text, size, and more.
@import "src/less/variables";
.input-group{
  position: relative;
  width: 100%;
  font-size: .8rem;
  color: @gray-75;
  border-radius: .2rem;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  .error-tips {
    display: none;
    font-size: .7rem;
    color: #FF4D4F;
  }
  .text{
    width: 100%;
    padding: .55rem .75rem;
    padding-right: 1.2rem;
    border: .1rem solid @gray-83;
    outline: none;
    border-radius: .25rem;
  }
  .l-info{
    display: inline-block;
    margin: .55rem 0;
    width: 3.8rem;
    padding-left: .75rem;
    border-right: 2px solid @gray-83;
  }
  .r-text{
    width: -webkit-calc(100% - 4.1rem);
    width: calc(100% - 4.1rem);
    border: none;
    outline: none;
    padding: .55rem .75rem;
    padding-right: 1.2rem;
  }
  .text-entry{
    border: .1rem solid @gray-83;
    border-radius: .25rem;
  }
  .clean-txt{
    display: none;
    position: absolute;
    padding: 0;
    right: .7rem;
    top: .75rem;
    width: 1.1rem;
    height: 1.3rem;
    color: @gray-dark;
    border: none;
    cursor: pointer;
    .icon-close{
      font-size: 1rem;
    }
  }
  .badge-info{
    position: absolute;
    left: 0;
    top: 2.85rem;
    z-index: 99;
    display: none;
  }

  &.filled{
    .clean-txt{
      display: block;
      display: none\0; //IE9使用自身效果
      &:focus{
        display: block !important;
        display: none\0; //IE9使用自身效果
        outline: none;
        color: @primary-color;
      }
    }
  }

  &.disabled{
    cursor: not-allowed;
    .text,.r-text,.text-entry, label{
      color: #DBD7D4;
    }
    .icon-zh {
      color: #DBD7D4;
    }
    .text {
      cursor: not-allowed;
      &:-ms-input-placeholder{
        color: #DBD7D4 !important;
      }
      &::-moz-placeholder{
        color: #DBD7D4 !important;
      }
      &:-moz-placeholder{
        color: #DBD7D4 !important;
      }
      &::-webkit-input-placeholder{
        color: #DBD7D4 !important;
      }
      &::placeholder{
        color: #DBD7D4 !important;
      }
    }
  }

  &:not(:disabled):not(.disabled):hover{
    .text, .text-entry{
      border: .1rem solid #4F3D1E;
    }
  }

  &:not(.disabled):not(.error).active{
    .text, .text-entry {
      border: .1rem solid #4F3D1E;
    }
  }

  &.error{
    .text, .text-entry{
      border: .1rem solid #FF4D4F;
      color: #FF4D4F;
    }
    &:not(:disabled):not(.disabled):hover{
      .text, .text-entry{
        border: .1rem solid #FF4D4F !important;
      }
    }
    .error-tips {
      display: block;
      position: absolute;
      margin-top: 5px;
      @media (max-width: @screen-xs-max) {
        position: initial;
      }
    }
  }
}

.input-single{
  padding-left: 0;
  position: relative;
  //font-family: @font-family-sans-Semibold;
  .label-wrap{
    position: absolute;
    top: 1px;
    left: 10px;
    right: 10px;
    // max-width: 90%;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  label{
    vertical-align: top;
    margin-bottom: 0;
    font-size: 14px;
    color: #4F3D1E;
    @media screen and (max-width: @screen-xs-max){
      font-size: 14px;
    }
  }
  &.input-group{
    position: relative;
    .text{
      padding: 25px 9px 6px;
      height: 66px;
      border: 1px solid transparent;
      border-radius: 0;
      background: none;
      border-bottom-color: @gray-lighter;
      font-size: 1rem;
      color: @gray-101010;
      text-overflow: ellipsis;
      @media screen and (max-width: @screen-xs-max){
        font-size: .8rem;
        height: 63px;
      }
      &:-ms-input-placeholder{
        color: #4F3D1E !important;
      }
      &::-moz-placeholder{
        color: #4F3D1E !important;
      }
      &:-moz-placeholder{
        color: #4F3D1E !important;
      }
      &::-webkit-input-placeholder{
        color: #4F3D1E !important;
      }
      &::placeholder{
        color: #4F3D1E !important;
      }
      &.no-support{
        color: #4F3D1E;
      }
    }
    .no-border-bottom {
      border-bottom-color: transparent;
    }
    &:not(.disabled):not(.error).active,
    &:not(:disabled):not(.disabled):hover{
      .label-wrap{
        // left: 10px;
      }
      .calendar{
        // right: 10px;
      }
      .text{
        // padding-left: 9px;
        // padding-right: 9px;
        border-radius: 4px;
        background: white;
        border: 1px solid #4F3D1E;
        box-shadow: 0 3px 8px 0 rgba(0,0,0,0.08);
      }
      // .bubble-info{
      //   left: -10px;
      //   right: -10px;
      // }
    }
    &.error{
      .text{
        padding-left: 9px;
        padding-right: 9px;
        border-radius: 4px;
        border: 1px solid #FF4D4F;
        box-shadow: 0 3px 8px 0 rgba(0,0,0,0.08);
        color: #FF4D4F;
      }
      .label-wrap{
        left: 10px;
      }
      // .bubble-info{
      //   left: -10px;
      // }
      &:not(:disabled):not(.disabled):hover{
        .text{
          border: 1px solid #FF4D4F !important;
          color: #FF4D4F;
        }
      }
    }
    &.filled{
      .clean-txt{
        &:focus{
          color: @purple-8c;
        }
      }
    }
    &.disabled{
      cursor: not-allowed;
      .text,.r-text,.text-entry, label{
        color: #DBD7D4;
      }
      .icon-zh {
        color: #DBD7D4;
      }
      .text {
        cursor: not-allowed;
        &:-ms-input-placeholder{
          color: #DBD7D4 !important;
        }
        &::-moz-placeholder{
          color: #DBD7D4 !important;
        }
        &:-moz-placeholder{
          color: #DBD7D4 !important;
        }
        &::-webkit-input-placeholder{
          color: #DBD7D4 !important;
        }
        &::placeholder{
          color: #DBD7D4 !important;
        }
      }
    }
  }
  .clean-txt{
    right: 10px;
    top: 35px;
    width: auto;
    height: auto;
    line-height: 1;
    color: @gray-dark;
  }
  .icon-Not-Available1{
    display: block;
  }
  .badge{
    padding: 0 10px;
    background: none;
    font-size: 14px;
  }
  .calendar{
    position: absolute;
    top: 33px;
    right: 10px;
    font-size: .8rem;
    color: #554E49;
    pointer-events: none;

    // @media (max-width: @screen-lg-max) {
    //   right: 0;
    // }
    @media (max-width:  @screen-md-max) {
      right: 10px;
    }
  }
  .icon-zh-right-type1 {
    .calendar();
    transform: rotate(90deg);
  }
  .prompt-bubble-sigh{
    .prompt-btn{
      margin-left: 8px;
    }
  }
}
