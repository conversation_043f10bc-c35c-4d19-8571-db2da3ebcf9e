/* ========================================================================
 * ZH: Input  v1.0.0  通用输入框
 * Author: zjjing
 * ========================================================================
 * Copyright 2024-~ Travelsky ICED
 * response input compoment
 * ======================================================================== */
+(function ($) {
    // 输入框聚焦事件
    $(document).on('focus', '.input-group input', function () {
        if (!$(this).parents(".input-group").hasClass("disabled")) {
            var val = $(this).val();
            var $par = $(this).parents(".input-group");
            $par.addClass("active");
            // 自动展开提示信息
            if ($par.find(".badge-info").length > 0) {
                $par.find(".badge-info").show();
            }
            // toggle 文本填充样式
            if (val.length > 0) {
                $par.addClass("filled");
            } else {
                $par.removeClass("filled");
            }
        }
    });

    // 输入框失焦事件
    $(document).on('blur', '.input-group input', function () {
        var $par = $(this).parents(".input-group");
        $par.removeClass("active");
        // 自动收起提示信息
        if ($par.find(".badge-info").length > 0) {
            $par.find(".badge-info").hide();
        }
    });

    // 输入框输入事件
    $(document).on('input', '.input-group input', function () {
        var val = $(this).val();
        var $par = $(this).parents(".input-group");
        // toggle 文本填充样式
        if (val.length > 0) {
            $par.addClass("filled");
        } else {
            $par.removeClass("filled");
        }
    });

    // 输入框clean事件
    $(document).on('click', '.input-group .clean-txt', function () {
        var $par = $(this).parents(".input-group");
        var obj = $par.find("input");
        // 清空文本
        if (obj.length > 0) {
            obj.val("");
            // 清除填充样式
            $par.removeClass("filled");
        }
    });

    //乘客选择框聚焦事件
    $('.input-passenger .text').focus(function () {
        var $inputWrap = $(this).parents('.input-passenger');
        var $this = $inputWrap.find('.passenger-count-wrap');
        $('.passenger-count-wrap').hide();
        if ($this.length) {
            $this.show();
        }
    });

    //乘客选择框全局关闭事件
    $(document).on('click', function (e) {
        var _hasThis = $(e.target).hasClass('input-passenger');
        var _hasParents = $(e.target).parents('.input-passenger').length;
        var _has = _hasThis || _hasParents;
        if (!_has) {
            $('.passenger-count-wrap').hide();
        }
    });

    //乘客类型切换事件
    $('.passenger-count-wrap .passenger-type').on('click', '.type', function(){
        var thisIndex = $(this).index();
        $(this).siblings().removeClass('active');
        $(this).addClass('active');
        $(this).closest('.passenger-count-wrap').find('.passenger-count-list').hide();
        $(this).closest('.passenger-count-wrap').find('.passenger-count-list').eq(thisIndex).show();
    });

    //日历选择框全局关闭事件
    $('.calendar-input').click(function () {
        $('.passenger-count-wrap').hide();
    });

    // placeholder兼容
    function isUnderIE9() {
        var name = window.navigator.appName;
        var version = window.navigator.appVersion.split(';');
        var simpleVersion = '';
        version.forEach(function (item) {
            if (item.indexOf('MSIE') !== -1) {
                simpleVersion = item.trim();
                return;
            }
        });
        simpleVersion = parseInt(simpleVersion.split(' ')[1]);
        return name === 'Microsoft Internet Explorer' && simpleVersion <= 9;
    }

    $('input').each(function () {
        if (isUnderIE9()) {
            var placeholder = $(this).attr('placeholder');
            var value = $(this).val();
            if (placeholder) {
                if (!value) {
                    $(this).attr('value', placeholder);
                    $(this).addClass('no-support');
                }
                $(this).focus(function () {
                    var newValue = $(this).val();
                    if (newValue === placeholder) {
                        $(this).val('');
                        $(this).removeClass('no-support');
                    }
                });
                $(this).blur(function () {
                    var $this = $(this);
                    setTimeout(function () {
                        var newValue = $this.val();
                        if (!newValue) {
                            $this.val(placeholder);
                            $this.addClass('no-support');
                        }
                    }, 100);
                });
            }
        }
    });

})(jQuery);