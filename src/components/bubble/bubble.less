.prompt-bubble {
  position: relative;
  display: inline-block;
  vertical-align: top;

  .bubble-info {
    left: 50%;
    width: 18rem;
    margin-left: -(18rem / 2);
  }
  &:after {
    content: "";
    position: absolute;
    z-index: 99;
    left: 50%;
    margin-left: -0.6rem;
    top: 100%;
    width: 0;
    height: 0;
    border-left: 0.6rem solid transparent;
    border-right: 0.6rem solid transparent;
    border-bottom: 0.6rem solid;
    color: #f8f1e5;
    display: none;
  }
  &.show {
    &:after {
      display: block;
    }
  }
  &-l {
    .bubble-info {
      margin-left: -5.5rem;
    }
  }
}

.bubble-info {
  position: absolute;
  z-index: 99;
  top: 100%;
  padding-top: 0.5rem;
  text-align: left !important;
  .desc {
    padding: 1rem 0.5rem;
    border-radius: 0.2rem;
    opacity: 1;
    background: #f8f1e5;
    text-align: justify;
    font-size: 0.7rem;
  }
  .text1 {
    margin-bottom: 10px;
    cursor: text;
    color: #4F3D1E;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 重置移动端样式，需要写在气泡的父级
.prompt-bubble-wrap{
  position: relative;
  > .bubble-info{
      left: 0;
      right: 0;
      padding-top: 0;
      .desc{
          box-shadow: 0 3px 8px 0 rgba(0,0,0,0.08);
          // color: $gray;
          color: #f8f1e5;
      }
  }
}