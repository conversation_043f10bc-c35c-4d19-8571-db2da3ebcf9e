/* ========================================================================
 * ZH: bubble  v1.0.0  通用气泡
 * Author: zjjing
 * ========================================================================
 * Copyright 2024-~ Travelsky ICED
 * response input compoment
 * ======================================================================== */
  $('.prompt-bubble').mouseenter(function () {
  activeBubble($(this));
}).mouseleave(function () {
  blurBubble($(this));
});

// 气泡键盘Tab focus事件
$('.prompt-bubble').focus(function(){
  activeBubble($(this));
});

// 气泡键盘Tab blur事件
$('.prompt-bubble').blur(function(){
  blurBubble($(this));
});

//气泡点击事件
$('.prompt-bubble').click(function() {
  var win_width = $(window).width();
  if (win_width >= 989) { return; }
  var _show = $(this).hasClass('show');
  if (_show) {
      $(this).removeClass('show');
      $(this).next('.bubble-info').remove();
  } else {
      var text = $(this).data('text');
      var html = '<div class="bubble-info"><div class="desc"><p class="text1">' + text + '</p></div></div>';
      $(this).addClass('show');
      $(this).parent().append(html);
  }
});

// 激活气泡
function activeBubble(obj) {
  var win_width = $(window).width(),
      _show = $(this).hasClass('show');
  // 仅PC端处理
  if (_show || win_width < 989) { return; }
  var text = obj.data('text');
  // 构建气泡Dom
  var html = '<div class="bubble-info"><div class="desc"><p class="text1">' + text + '</p></div></div>';
  obj.addClass('show');
  obj.append(html);
}

// 移除气泡
function blurBubble(obj) {
  var win_width = $(window).width();
  // 仅PC端处理
  if (win_width < 989) { return; }
  // 移除气泡Dom
  obj.removeClass('show');
  obj.find('.bubble-info').remove();
}

// 气泡全局关闭
$(document).on('click', function (e) {
  if ($(window).width() > 1025) return;
  var _hasThis = $(e.target).parents('.prompt-bubble').length;
  var _hasParents = $(e.target).parents('.bubble-info').length;
  var _has = _hasThis || _hasParents;

  if (!_has) {
      $('.prompt-bubble').removeClass('show');
      $('.bubble-info').remove();
  }
});
