/**
 *
 * @param {Array<Record<string, any>>} selectList 列表项目
 * @param {{labelFiled: string, valueFiled: string, differentiation: {targets: Array<any>, classNames: Array<string>}}} [option={labelFiled: 'label', valueFiled: 'id'}]
 *
 * option :
 *    -> labelFiled: 用于显示的字段
 *    -> valueFiled: 用于存储数据的字段
 *    -> differentiation: 对一些项目进行差异化配置
 *        ->targets: 差异化配置的目标 对应数据的 valueFiled
 *        ->classNames: 差异化配置的类名
 */
HTMLInputElement.prototype.openDropdownSelect = function (selectList, option = {}) {
  let popperRef = null;

  option = Object.assign({
    labelFiled: 'label',
    valueFiled: 'id',
    defaultValue: null,
    differentiation: {
      targets: [],
      classNames: []
    }
  }, option);

  if (option.defaultValue !== null && !this.value) {
    const defaultItem = selectList.find(item => item[option.valueFiled] === option.defaultValue);
    if (defaultItem) {
      this.value = defaultItem[option.labelFiled];
      this.dataset.valueFiled = defaultItem[option.valueFiled];
    }
  }

  const inputValue = this.value;
  let tabIndex = this.getAttribute('tabindex') || '200';
  tabIndex = parseInt(tabIndex) + 1;

  const maskRef = document.createElement('div');
  // maskRef.classList.add('__dropdown-wrap');

  const chooseOption = (optionIndex) => {
    this.value = selectList[optionIndex].label;
  };

  const onKeyDown = (event, optionIndex) => {
    if (event.key === 'Enter') {
      chooseOption(optionIndex);
      if (maskRef) maskRef.click();
    }
  };

  const closeDropdown = () => {
    if (popperRef) {
      popperRef.destroy();
      popperRef = null;
    }
    maskRef.remove();

    this.removeEventListener('blur', closeDropdown);
  };

  maskRef.addEventListener('click', closeDropdown);

  const dropDownWrapDom = document.createElement('div');
  dropDownWrapDom.classList.add('__dropdown-wrap');

  dropDownWrapDom.style.width = `${this.getBoundingClientRect().width}px`;

  const selectListDom = selectList.map((item, index) => {
    const isDifferentiation = option.differentiation.targets.includes(item[option.valueFiled]);

    let className = `__dropdown-item ${item[option.labelFiled] === inputValue ? '__dropdown-item-active' : ''}`;
    if (isDifferentiation) className += ` ${option.differentiation.classNames.join(' ')}`;

    return `<div class="${className}" tabindex="${tabIndex++}" 
      data-value="${item[option.valueFiled]}">${item[option.labelFiled]}</div>
    `;
  });

  dropDownWrapDom.insertAdjacentHTML('afterbegin', selectListDom.join(' '));
  maskRef.append(dropDownWrapDom);

  document.body.append(maskRef);
  maskRef.addEventListener('focus', maskRef.click);

  dropDownWrapDom.querySelectorAll('.__dropdown-item').forEach((item, index) => {
    item.addEventListener('click', (event) => {
      this.value = selectList[index][option.labelFiled];
      this.dataset.valueFiled = selectList[index][option.valueFiled];
      $(this).change();
      maskRef.click();
    });
    item.addEventListener('keydown', (event) => onKeyDown(event, index));
  });

  this.addEventListener('blur', () => setTimeout(() => closeDropdown(), 200));

  popperRef = Popper.createPopper(this, dropDownWrapDom, {
    placement: 'bottom',
    modifiers: [
      {
        name: 'offset',
        options: {
          offset: [0, 8],
        },
      },
    ],
  });
};
