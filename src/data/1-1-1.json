{"productZoneData": [{"imageSrc": "./../../images/middleImg/product-zone-img-1.jpg", "title": "Package Zone", "ariaLabel": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "description": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with th experience bestMake the perfect holiday trip with th experience bestMake the perfect holiday trip with th experience bestMake the perfect holiday trip with the best", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Package Zone"}, {"imageSrc": "./../../images/middleImg/product-zone-img-2.jpg", "title": "Student Area Student AreaStudent Area", "ariaLabel": "Book tickets with a favorable code to stay at ease", "description": "Book tickets with a favorable code to stay at ease", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Student Area Student AreaStudent Area"}, {"imageSrc": "./../../images/middleImg/product-zone-img-1.jpg", "title": "Package Zone", "ariaLabel": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "description": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Package Zone"}, {"imageSrc": "./../../images/middleImg/product-zone-img-2.jpg", "title": "Student Area Student AreaStudent Area", "ariaLabel": "Book tickets with a favorable code to stay at ease", "description": "Book tickets with a favorable code to stay at ease", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Student Area Student AreaStudent Area"}, {"imageSrc": "./../../images/middleImg/product-zone-img-1.jpg", "title": "Package Zone", "ariaLabel": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "description": "Make the perfect holiday trip with the best offers and explore the b life experience bestMake the perfect holiday trip with the best", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Package Zone"}, {"imageSrc": "./../../images/middleImg/product-zone-img-2.jpg", "title": "Student Area Student AreaStudent Area", "ariaLabel": "Book tickets with a favorable code to stay at ease", "description": "Book tickets with a favorable code to stay at ease", "arrowIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Student Area Student AreaStudent Area"}], "travelRecommendationsData": [{"imgSrc": "./../../images/middleImg/travel-recommend-img-1.jpg", "alt": "Doha", "place": "Doha", "description": "Doha Airport - Hamad International Airport (DOH)Doha Airport - Hamad International Airport (DOH)Doha Airport - Hamad International Airport (DOH)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-2.jpg", "alt": "London", "place": "London", "description": "London Airport - Heathrow Airport (LHR)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-3.jpg", "alt": "Seoul", "place": "Seoul", "description": "Seoul Airport - Incheon International Airport (ICN)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-4.jpg", "alt": "Bangkok", "place": "Bangkok", "description": "Bangkok Airport - Suvannap International Airport (BKK)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-1.jpg", "alt": "Doha", "place": "Doha", "description": "Doha Airport - Hamad International Airport (DOH)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-2.jpg", "alt": "London", "place": "London", "description": "London Airport - Heathrow Airport (LHR)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-3.jpg", "alt": "Seoul", "place": "Seoul", "description": "Seoul Airport - Incheon International Airport (ICN)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-4.jpg", "alt": "Bangkok", "place": "Bangkok", "description": "Bangkok Airport - Suvannap International Airport (BKK)", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "moreIconAlt": "learn more"}], "travelRecommendationsMobileData": [{"imgSrc": "./../../images/middleImg/travel-recommend-img-1.jpg", "alt": "Doha", "place": "Doha"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-2.jpg", "alt": "London", "place": "London"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-3.jpg", "alt": "Seoul", "place": "Seoul"}, {"imgSrc": "./../../images/middleImg/travel-recommend-img-4.jpg", "alt": "Bangkok", "place": "Bangkok"}], "productRecommendationData": [{"imgSrc": "./../../images/middleImg/product-recommend-1.jpg", "title": "Preferred seat", "description": "Every distance you want to go, I accompany you, prepaid seat selection, your favorite seat 'lock' in advancselection, your favorite seat 'lock' in advancselection, your favorite seat 'lock' in advancselection, your favorite seat 'lock' in advance!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Preferred seat"}, {"imgSrc": "./../../images/middleImg/product-recommend-2.jpg", "title": "Special meals", "description": "A Star Alliance ticket, an unlimited journey.", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Special meals"}, {"imgSrc": "./../../images/middleImg/product-recommend-3.jpg", "title": "Extra Baggage", "description": "Price surprise, excess value 8% off Easy shipping and saving time and heart saving", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Extra Baggage"}, {"imgSrc": "./../../images/middleImg/product-recommend-4.jpg", "title": "Hotel Transit", "description": "Become a member of Star Alliance Gold Card and enjoy exclusive courtesy.", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Hotel Transit"}], "eventRecommendationData": [{"imgSrc": "./../../images/middleImg/event-recommendation-img-1.jpg", "title": "Flying Takers ChallengeFlying Takers ChallengeFlying Takers ChallengeFlying Takers Challenge", "description": "Fly the sky, challenge unlimited! Wait for you to conquer the blue sky limit!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Flying Takers Challenge"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-2.jpg", "title": "Elevator coupon", "description": "Upgrade experience, start with a booster ticket!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Elevator coupon"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-3.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-1.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-2.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}, {"imgSrc": "./../../images/middleImg/event-recommendation-img-3.jpg", "title": "Transfer accommodation", "description": "A warm resting station on the journey!", "moreText": "Learn more", "moreIconSrc": "./../../images/middleImg/icon-arrow-left.svg", "alt": "Transfer accommodation"}], "sevenDayCalendarData": [{"outbound": "06-20", "outboundDay": "<PERSON><PERSON>", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "920"}}, {"outbound": "06-21", "outboundDay": "Sat", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-22", "outboundDay": "Sun", "inbound": {"06-22": "-", "06-23": "1,920", "06-24": "1,920", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-23", "outboundDay": "Mon", "inbound": {"06-22": "-", "06-23": "1,920", "06-24": "-", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-24", "outboundDay": "<PERSON><PERSON>", "inbound": {"06-22": "-", "06-23": "-", "06-24": "1,920", "06-25": "-", "06-26": "920", "06-27": "920", "06-28": "1,920"}}, {"outbound": "06-25", "outboundDay": "Wed", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "1,920", "06-27": "1,920", "06-28": "1,920"}}, {"outbound": "06-26", "outboundDay": "<PERSON>hu", "inbound": {"06-22": "-", "06-23": "-", "06-24": "-", "06-25": "-", "06-26": "1,920", "06-27": "1,920", "06-28": "1,920"}}], "flightData": {"departureTime": "12:30", "departureAirport": "Shenzhen Bao'an International Airport", "arrivalTime": "14:35", "arrivalAirport": "Beijing Capital International Airport", "duration": "2h 5min", "flightNumber": "ZH8034", "aircraft": "Airbus A320", "sharing": true, "mealAvailable": true, "flightDetailsLink": "#", "priceOptions": [{"class": "Economy Class", "price": "13,999"}, {"class": "Premium Economy", "price": "13,999"}, {"class": "Business Class", "price": "13,999"}], "bookingClasses": [{"name": "Business Travel Economy Class", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "economy"}, {"name": "Eco Super Value", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "economy"}, {"name": "Business Travel Economy Class", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "premium-economy"}, {"name": "Eco Super Value", "remaining": 2, "price": "7,999", "taxesAndFees": "7,099", "fare": "900", "cabinClass": "U+A", "baggage": "1 Pieces", "refund": "300+", "change": "300+", "baseMileage": "+1142", "bonusMileage": "+1142", "type": "business"}]}}