/**
 * 给所有指定 [data-tabIndex] 的元素添加一个自增的 tabIndex 值
 * @param indexGap
 */
const addTabIndexByDataTab = (indexGap = 5) => {
    const elements = document.querySelectorAll('[data-tabIndex]');
    elements.forEach((element, index) => {
        const hasRole = element.hasAttribute('role');
        const hasTabIndex = element.hasAttribute('tabIndex');
        const hasExpanded = element.hasAttribute('aria-expanded');
        const hasLabel = element.hasAttribute('aria-label');

        if (!hasRole) element.role = "button";
        if (!hasTabIndex) element.tabIndex = (index + 1) * indexGap;
        if (!hasExpanded) element.setAttribute('aria-expanded', "false");
        if (!hasLabel) {
            let label = "等待手动填入 aria-label";
            if (element.innerText) label = /* 去除大量换行带来的空格 */ element.innerText.replace(/\s{2,}/g, '');

            element.setAttribute('aria-label', label);
        }
    });
};

// addTabIndexByDataTab()
  
  