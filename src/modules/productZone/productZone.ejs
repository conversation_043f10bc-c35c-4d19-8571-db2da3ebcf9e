<div>
    <% if (data.productZoneData.length === 1) { %>
        <div class="product-zone card-solo">
            <div class="card-solo-info">
                <div class="card-solo-title"><%= data.productZoneData[0].title; %></div>
                <div class="card-solo-description" title="<%= productZoneData[0].description %>"><%= data.productZoneData[0].description; %></div>
                <a class="no-style more __text-button" role="button">
                    <span class="__text-button-label">Learn more</span>
                    <img alt="learn more" class="__text-button-icon" src="<%= productZoneData[0].arrowIconSrc %>">
                </a>
            </div>
            <div class="card-solo-img">
                <img alt="<%= data.productZoneData[0].alt %>" class="card-img" src="<%= productZoneData[0].imageSrc %>">
            </div>
        </div>
    <% } else { %>
            <div class="swiper pc-scroll-wrap-big">
                <!-- 如果需要导航按钮 -->
                <button class="no-style slide-icon slide-icon-left"
                        onfocusin="toPrev1()"
                        onclick="toPrev1()"
                        data-tabOver
                        id="swiper1LeftArrow">
                    <img alt="previous" src="images/middleImg/icon-slide.svg">
                </button>

                <button class="no-style slide-icon slide-icon-right"
                        onclick="toNext1()"
                        id="swiper1RightArrow"
                        onfocusin="toNext1()">
                    <img alt="next" src="images/middleImg/icon-slide.svg">
                </button>
                <div class="swiper-wrapper">

                    <% data.productZoneData.forEach((card, index) => { %>
                        <div class="swiper-slide pointer-card" role="button" onclick="jumpToUrl('#')">
                            <div class="scroll-wrap-inner row gap-PC-4">
                                <div class="card col-12">
                                    <img alt="<%= card.alt %>" class="card-img" src="<%= card.imageSrc %>">

                                    <div class="card-content">
                                        <div aria-label="<%= card.title %>" class="card-title" title="<%= card.title ;%>">
                                            <%= card.title ;%>
                                            <%= index ;%>
                                        </div>
                                        <div aria-expanded="false"
                                             aria-label="<%= card.ariaLabel %>"
                                             class="card-desc double-line" title="<%= card.description %>">
                                            <%= card.description %>
                                        </div>

                                        <a class="no-style more __text-button" role="button">
                                            <span class="__text-button-label">Learn more</span>
                                            <img alt="learn more" class="__text-button-icon" src="<%= card.arrowIconSrc %>">
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% }); %>
                </div>
                <div class="swiper1-pagination"></div>
            </div>
    <% } %>
</div>
