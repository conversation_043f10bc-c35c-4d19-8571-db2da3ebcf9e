@var1: #ee2934;
@var2: #000000;
@var3: #979797;

.bg-con {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	background-image: url(../images/bg.jpg);
	background-size: cover;
	background-repeat: no-repeat;
	background-attachment: fixed;
	.load-logo {
		margin-bottom: 40px;
	}
	.load-con {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateX(-50%);
        transform: translate(-50%, -50%);
        width: 430px;
        font-size: 0.8rem;
        padding: 0 .5rem;

        @media screen and (max-width:  @screen-xs-max) {
            width: 375px;
        }
	}
}

.load-con {
	text-align: center;
	line-height: 2;
	display: block;
	.load-img {
		margin: 0 auto;
		color: @var1;
		width: 140px;
		height: 140px;
		line-height: 14px;
		p {
			margin-top: -77px;
		}
	}
	.load-txt {
		margin-top: 25px;
	}
}
