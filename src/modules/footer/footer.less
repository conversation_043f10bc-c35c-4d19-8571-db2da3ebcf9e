@media screen and (min-width : 1440px) {
}

@media screen and (min-width : 1025px) /* PC 端 */ {
  .ipad-footer, .mobile-footer {
    display : none;
  }

  .pc-footer {

    &-top {
      background-color    : #F1EFEE;
      width               : 100%;

      background-image    : url("../images/footerIcon/footer-background-img.png");
      background-repeat   : no-repeat;
      background-position : calc(50% + 33rem) 0;
      background-size     : 19rem 19.86rem;

      &-inner {
        margin          : 0 auto;
        display         : flex;
        padding         : 2.6rem 0 3.25rem;
        align-items     : center;
        justify-content : space-around;

        .footer-top-item {
          display         : flex;
          flex-direction  : column;
          align-items     : center;
          justify-content : center;

          img {
            width         : 3.8rem;
            height        : 3.8rem;
            margin-bottom : 1.1rem;
          }

          .label {
            font-size : 1rem;
            color     : #101010;
          }


        }
      }
    }

    &-bottom {
      background-color : #2C2420;
      color            : #FFFFFF;

      &-inner {
        margin : 0 auto;

        .footer-bottom-line1 {
          display         : flex;
          align-items     : center;
          justify-content : center;
          font-size       : 0.7rem;
          padding         : 1.3rem 0 1rem;

          a {
            color : #ffffff;
          }

          .split {
            width  : 0.05rem;
            color  : #D6D3CE;
            margin : 0 1rem;
          }
        }

        .line {
          height           : 0.05rem;
          background-color : #4D4D4D;
          width            : 100%;
        }

        .footer-bottom-line2 {
          padding         : 1.15rem 0 1.2rem;
          display         : flex;
          align-items     : center;
          justify-content : center;

          .content {
            font-size    : 0.7rem;
            margin-right : 1.55rem;
          }

          img {
            width  : 13.25rem;
            height : 1.3rem;
          }
        }
      }
    }
  }
}

@media screen and (max-width : 1024px) /* Ipad */ {
  .pc-footer, .mobile-footer {
    display : none;
  }

  .ipad-footer {
    display : block;

    &-top {
      background-color    : #F1EFEE;

      background-image    : url("../images/footerIcon/footer-background-img.png");
      background-repeat   : no-repeat;
      background-position : calc(100% - 0.5rem) 0;
      background-size     : 19.9rem 20.75rem;

      &-inner {
        margin          : 0 auto;
        display         : flex;
        padding         : 2.6rem 0 3.25rem;

        align-items     : center;
        justify-content : space-around;

        .footer-top-item {
          flex            : 1;
          flex-shrink     : 0;
          display         : flex;
          flex-direction  : column;
          align-items     : center;
          justify-content : space-between;

          img {
            width         : 3.8rem;
            height        : 3.8rem;
            margin-bottom : 1.1rem;
          }

          .label {
            font-size   : 1rem;
            line-height : 1.5rem;
            color       : #101010;
            text-align  : center;
          }
        }
      }
    }

    &-bottom {
      background-color : #2C2420;
      color            : #FFFFFF;

      &-inner {
        width  : 100%;
        margin : 0 auto;

        .footer-bottom-line1 {
          margin          : 0 auto 0.6rem;
          display         : flex;
          flex-wrap       : wrap;
          align-items     : center;
          justify-content : center;
          font-size       : 0.7rem;

          .top-item {
            color : #ffffff;
          }

          &:first-child {
            padding-top : 1.3rem;
          }

          &:nth-of-type(2) {
            padding-bottom : 0.4rem;

            .top-item {
              margin-bottom : 0;
            }
          }

          .split {
            width  : 0.05rem;
            color  : #D6D3CE;
            margin : 0 1rem 0;
          }
        }

        .line {
          height           : 0.05rem;
          background-color : #4D4D4D;
          width            : 100%;
        }

        .footer-bottom-line2 {
          padding         : 1.15rem 0 1.2rem;
          display         : flex;
          align-items     : center;
          justify-content : center;
          margin          : 0 -20px;

          .content {
            margin-right : 1.55rem;
            font-size    : 0.7rem;
          }
        }
      }
    }
  }
}

@media screen and (max-width : 767px) /* 移动端 */ {
  .pc-footer, .ipad-footer {
    display : none;
  }

  .mobile-footer {
    display : block;

    &-top {
      background-color    : #F1EFEE;
      width               : 100%;
      background-image    : url("../images/footerIcon/footer-background-img.png");
      background-repeat   : no-repeat;
      background-position : calc(100% - 0.6rem) 4.8rem;

      &-inner {
        margin                : 0 auto;
        padding               : 1.85rem 0 1.7rem;
        display               : grid;
        grid-template-columns : repeat(2, 1fr);
        place-items           : center;
        grid-row-gap          : 2rem;

        .footer-top-item {
          display         : flex;
          flex-direction  : column;
          align-items     : center;
          justify-content : center;

          img {
            width         : 2.95rem;
            height        : 2.95rem;
            margin-bottom : 0.65rem;
          }

          .label {
            font-size   : 0.8rem;
            line-height : 1.5rem;
            color       : #101010;
            text-align  : center;
          }
        }
      }
    }

    &-bottom {
      background-color : #2C2420;
      color            : #FFFFFF;

      &-inner {
        width  : 100%;
        margin : 0 auto;

        .footer-bottom-line1 {
          margin          : 0 auto;
          display         : flex;
          flex-wrap       : wrap;
          align-items     : center;
          justify-content : center;
          font-size       : 0.6rem;

          .top-item {
            color : #ffffff;
          }

          &:first-child {
            padding-top : 1.3rem;
          }

          &:nth-of-type(3) {
            padding-bottom : 1rem;
          }

          .split {
            width  : 0.05rem;
            color  : #D6D3CE;
            margin : 0 0.5rem 0.6rem;
          }

          .top-item {
            margin-bottom : 0.6rem;
          }
        }

        .line {
          height           : 0.05rem;
          background-color : #4D4D4D;
          width            : 100%;
        }

        .footer-bottom-line2 {
          padding         : 0.5rem 0 1.5rem;
          display         : flex;
          flex-direction  : column;
          align-items     : center;
          justify-content : center;

          .content {
            text-align : center;
            font-size  : 0.55rem;
          }

          img {
            margin-top    : 0.8rem;
          }
        }
      }
    }
  }
}
