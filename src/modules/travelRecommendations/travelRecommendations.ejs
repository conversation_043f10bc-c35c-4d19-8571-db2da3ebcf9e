<div class="swiper pc-scroll-wrap">
  <a
    href="javascript:"
    role="button"
    id="swiper2LeftArrow"
    class="slide-icon swiper2-btn-left slide-icon-left no-style"
    onclick="toPrev2()"
    onfocusin="toPrev2()"
  >
    <img alt="" src="images/middleImg/icon-slide.svg" />
  </a>

  <a
    href="javascript:"
    role="button"
    id="swiper2RightArrow"
    class="slide-icon swiper2-btn-right slide-icon-right no-style"
    onclick="toNext2()"
    onfocusin="toNext2()"
  >
    <img alt="" src="images/middleImg/icon-slide.svg" />
  </a>

  <!-- Travel Recommendations轮播 PC和pad端数据-->
  <div class="swiper-wrapper">
    <% data.travelRecommendationsData.forEach(function(card, index) { %>
    <div class="swiper-slide pointer-card" onclick="jumpToUrl('#')">
      <div class="pc-scroll-wrap-inner row gap-4">
        <div class="card col-12">
          <img
            alt="<%= card.alt ;%>"
            class="card-img"
            src="<%= card.imgSrc ;%>"
          />

          <div class="card-content">
            <div class="place" title="<%= card.place ;%>">
              <img alt="" src="images/middleImg/icon-fligt.svg" />
              <span><%= card.place ;%></span>
            </div>

            <div class="desc double-line" title="<%= card.description ;%>">
              <%= card.description ;%>
            </div>
          </div>

          <a class="more no-style __text-button" role="button">
            <span class="__text-button-label"><%= card.moreText ;%></span>
            <img
              class="__text-button-icon"
              alt="<%= card.moreIconAlt ;%>"
              src="<%= card.moreIconSrc ;%>"
            />
          </a>
        </div>
      </div>
    </div>
    <% }); %>
  </div>

  <div class="swiper2-pagination"></div>
</div>

<!-- Travel Recommendations轮播 手机端数据-->
<div class="pc-scroll-mobile-wrap row gap-2">
  <% data.travelRecommendationsMobileData.forEach(function(card) { %>
  <div class="card col-12">
    <img alt="<%= card.alt ;%>" class="card-img" src="<%= card.imgSrc ;%>" />

    <div class="card-content">
      <div class="place" title="<%= card.place ;%>">
        <img alt="learn more" src="images/middleImg/icon-fligt.svg" />
        <span><%= card.place ;%></span>
      </div>
    </div>
  </div>
  <% }); %>
</div>
