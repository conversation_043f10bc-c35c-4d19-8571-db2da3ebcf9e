@import "../../less/mediaMixin.less";

.swiper-pagination-bullets {
  text-align : center;
  margin-top : .5rem;

  .swiper-pagination-bullet {
    width            : .6rem;
    height           : .6rem;
    background-color : #CCCCCC;
    opacity          : 1 !important;
    margin-right     : .7rem !important;

    &:last-child {
      margin-right : 0 !important;
    }

    &-active {
      background-color : #CC0100;
    }
  }
}


.pc-page-body {
  display : block;
  margin  : 0 auto;

  .section {
    position      : relative;
    margin-bottom : 3.5rem;
    .screenPad({
      margin-bottom : 2.5rem;
    });

    .screenMobile({
      margin-bottom : 2rem;
    });

    &-title {
      font-size     : 1.4rem;
      line-height   : 2rem;
      margin-bottom : 1.5rem;
      margin-top    : 3.5rem;

      .screenMobile({
        font-size     : 1.2rem;
        margin-bottom : 1rem;
        margin-top    : 2rem;
      })
    }

    .slide-icon {
      position : absolute;
      top      : 44%;
      width    : 4%;
      z-index  : 5;
      display  : none;

      .screenPC({
        display : block;
      });

      .screenSPC({
        display : block;
      });

      &-left {
        left      : 3px;
        transform : rotate(180deg);
      }

      &-right {
        right : 3px;
      }

      img {
        width  : 100%;
        height : 100%;
      }
    }

    .pc-scroll-wrap-big {
      display : block;
      padding : 3px;

      .swiper1-pagination {

        .screenPC({
          display : none;
        });

        .screenSPC({
          display : none;
        })
      }

      .scroll-wrap-inner {

        .card {
          position : relative;

          &-img {
            width        : 100%;
          }

          &-content {
            padding    : 1rem 1.5rem;
            box-sizing : border-box;
            position   : relative;
            height     : 8.7rem;
            border     : 0.05rem solid #CCCCCC;

            .screenMobile({
              padding : 1rem;
              height  : auto;
            });

            .card-title {
              display: block;
              line-height   : 2.05rem;
              font-size     : 1.2rem;
              font-weight   : 500;
              color         : #101010;
              margin-bottom : 0.5rem;

              text-overflow : ellipsis;
              overflow      : hidden;
              word-break    : break-all;
              white-space   : nowrap;
              .screenMobile({
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                white-space: normal;
              })
            }

            .card-desc {
              font-size     : 0.8rem;
              color         : #7A6A5E;
              line-height   : 1.15rem;
              margin-bottom : 1rem;
              word-break    : break-all;

              .screenMobile({
                -webkit-line-clamp : 3;
              })
            }
          }

          .more {
            position        : absolute;
            right           : 1rem;
            bottom          : 1rem;
            justify-content : flex-end;
            z-index: 2;
          }
        }
      }
    }

    .pc-scroll-wrap {
      display : block;
      padding : 3px;

      .swiper2-pagination {
        display : block;

        .screenPC({
          display : none;
        });

        .screenSPC({
          display : none;
        })
      }

      .screenMobile({
        display : none;
      });

      .slide-icon {
        position   : absolute;
        top        : 33%;
        width      : 4%;
        object-fit : contain;
        cursor     : pointer;
        z-index    : 5;

        &-left {
          left      : 3px;
          transform : rotate(180deg);
        }

        &-right {
          right : 3px;
        }
      }

      &-inner {
        .card {
          position : relative;

          &-img {
            width          : 100%;
            vertical-align : top;
          }

          &-content {
            padding    : 1rem 1.5rem;
            box-sizing : border-box;
            border     : 0.05rem solid #CCCCCC;
            height     : 8.5rem;

            .screenMobile({
              padding : 1rem;
            });

            .place {
              display       : flex;
              align-items   : center;

              font-size     : 1rem;
              line-height   : 1.5rem;
              font-weight   : 500;

              margin-bottom : 0.55rem;

              img {
                width        : 0.8rem;
                height       : 0.8rem;
                margin-right : 0.4rem;
              }
            }

            .desc {
              color         : #7A6A5E;
              font-size     : 0.8rem;
              text-align    : justify;
              line-height   : 1.5em;
              margin-bottom : 1rem;
              word-break    : break-all;
            }
          }

          .more {
            position      : absolute;
            right         : 1rem;
            bottom        : 1rem;
            margin-bottom : 0 !important;
            z-index: 2;

          }
        }
      }
    }

    .pc-scroll-mobile-wrap {
      display : none;

      .screenMobile({
        display : block;
      });

      .card {
        //position : relative;

        &-img {
          width          : 100%;
          vertical-align : top;
        }

        &-content {
          padding    : 1rem 1.5rem;
          box-sizing : border-box;
          border     : 0.05rem solid #CCCCCC;

          .place {
            display     : flex;
            align-items : center;

            font-size   : .8rem;
            line-height : 1.5rem;
            font-weight : 600;

            img {
              width        : 0.8rem;
              height       : 0.8rem;
              margin-right : 0.4rem;
            }
          }
        }
      }
    }

    .content {
      display       : flex;
      margin-bottom : 3.5rem;

      .screenPad({
        flex-direction : column;
      });

      .screenMobile({
        margin-bottom : 2rem;
      });

      .card-img {
        min-height : 7rem;
        object-fit : cover;
      }

      .info-card {
        border     : 0.05rem solid #CCCCCC;
        padding    : 1.5rem 1rem 1rem;
        box-sizing : border-box;
        .screenMobile({
          padding : 1rem;
        });

        &-title {
          font-size     : 1.2rem;
          line-height   : 2.05rem;
          font-weight   : 500;
          margin-bottom : 0.5rem;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          white-space: normal;

          .screenMobile({
            line-height: initial;
            white-space: initial;
            overflow: auto;
          })
        }

        .text {
          font-size     : 0.8rem;
          color         : #7A6A5E;
          line-height   : 1.5em;
          margin-bottom : 2.5rem;
          display       : -webkit-box;
          min-height        : 5rem;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 4;
          overflow      : hidden;
          text-overflow : ellipsis;
          .screenPad({
            height      : auto;
            display     : block;
          });
          .screenMobile({
            display       : block;
            margin-bottom : 1.5rem;
          })
        }

        .btn {
          //color         : #ffffff;
          //background    : #CC0100;
          width : 6rem;
          //height        : 2rem;
          //line-height   : 2rem;
          //text-align    : center;
          //font-size     : 0.8rem;
          //cursor        : pointer;
          float : right;
          //border-radius : 0.4rem;
          //transition    : .2s;

          .screenMobile({
            width : 100%;
          })
        }

        .more {
          display     : flex;
          align-items : center;
          color       : #7A6A5E;
          font-size   : 0.8rem;
          cursor      : pointer;
          z-index: 2;

          img {
            margin-left : 0.15rem;
            width       : 0.85rem;
            height      : 0.75rem;
            object-fit  : contain;
          }
        }
      }
    }

    .grid-card {

      .card {
        display     : flex;
        align-items : center;

        .screenMobile({
          width          : 100%;
          flex-direction : column;
        });

        &-img {
          width       : 50%;
          flex-shrink : 0;
          height      : 9.55rem;
          object-fit  : cover;

          .screenMobile({
            width : 100%;
            height: auto;
            object-fit: initial;
          })
        }

        &-content {
          width       : 50%;
          flex-shrink : 0;
          padding     : 1.5rem;
          box-sizing  : border-box;
          position    : relative;
          word-break  : break-all;
          height      : 9.55rem;
          border      : 0.05rem solid #CCCCCC;
          //max-height: 8.3lh;

          .screenMobile({
            width   : 100%;
            height  : auto;
            padding : 1rem;
          });

          .card-title {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            white-space: normal;
            text-overflow : ellipsis;
            overflow      : hidden;
            font-size     : 1rem;
            line-height   : 1.45rem;
            font-weight   : 500;
            margin-bottom : 0.5rem;

            .screenMobile({
              margin-bottom : 0;
            })
          }

          .card-text {
            font-size     : 0.75rem;
            line-height   : 1.2rem;
            color         : #7A6A5E;
            margin-bottom : 0.35rem;

            .screenMobile({
              display : none;
            });
          }

          .btn {
            position      : absolute;
            right         : 1rem;
            bottom        : 1rem;
            z-index: 2;
            //display       : flex;
            //align-items   : center;
            margin-bottom : 0 !important;
            //cursor        : pointer;

            .screenMobile({
              display : none;
            });

            //img {
            //  margin-left : 0.15rem;
            //  width       : 0.8rem;
            //  height      : 0.8rem;
            //}
          }
        }
      }
    }

    .pc-recommendation-wrap {
      width    : 100%;
      display  : flex;
      gap      : 1rem;
      position : relative;

      .screenPad({
        width : calc(100% + 1.5rem);
      });

      .screenMobile({
        width : calc(100% + .75rem);
      });

      &::before {
        position         : absolute;
        content          : "";
        top              : 0;
        left             : 0;
        z-index          : 3;
        width            : 5.5rem;
        height           : 20.55rem;
        background-image : linear-gradient(to right, rgba(255, 255, 255, .9), rgba(255, 255, 255, 0) 90%);

        .screenMobile({
          display : none;
        });

        .screenPad({
          display : none;
        })
      }

      &::after {
        position         : absolute;
        content          : "";
        top              : 0;
        right            : 0;
        z-index          : 3;
        width            : 5.5rem;
        height           : 20.55rem;
        background-image : linear-gradient(to left, rgba(255, 255, 255, .9), rgba(255, 255, 255, 0) 90%);
      }
      &.hide-before-after {
        &::before,
        &::after {
          display : none;
        }
      }

      .slide-icon-right {
        top   : 8.5rem;
        right : 1rem;
      }

      .slide-icon-left {
        top  : 8.5rem;
        left : 1rem;
      }

      .recommendation-card {
        flex-shrink : 0;
        height      : 100%;
        width       : 20rem;
        box-sizing  : border-box;
        border      : 0.05rem solid #CCCCCC;
        padding     : 0.5rem;
        position    : relative;

        .screenMobile({
          width : 15rem;
        });

        &-img {
          width      : 100%;
          // height     : 12.2rem;
          // object-fit : cover;
        }

        &-info {
          padding    : 1.5rem 0 1.5rem 0.5rem;
          box-sizing : border-box;
          height     : 7.4rem;

          .screenPad({
            padding : 1rem .5rem;
          });

          .screenMobile({
            height: auto
          });

          &-title {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            white-space: normal;
            text-overflow : ellipsis;
            overflow      : hidden;
            color         : #101010;
            font-size     : 1rem;
            line-height   : 1.35rem;
            font-weight   : 500;
            margin-bottom : 0.45rem;

            .screenMobile({
              margin-bottom : 0;
            })
          }

          &-desc {
            color       : #7A6A5E;
            font-size   : 0.8rem;
            line-height : 1.2rem;

            .screenMobile({
              display : none;
            })
          }
        }

        .more {
          position : absolute;
          bottom   : 0.95rem;
          right    : 0.9rem;
          z-index: 2;
          .screenMobile({
            display : none;
          })
          //display     : flex;
          //align-items : center;
          //color       : #7A6A5E;
          //font-size   : 0.8rem;
          //cursor      : pointer;
          //
          //img {
          //  margin-left : 0.15rem;
          //  width       : 0.85rem;
          //  height      : 0.75rem;
          //  object-fit  : contain;
          //}
        }
      }
    }
  }
}
.star-alliance-content {
  position: relative;
  .tab-a {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    z-index: 1;
  }
}

.pointer-card {
  cursor: pointer;
}

.product-zone  {
  &.card-solo {
    display: flex;
    .card-solo-img, .card-solo-info {
      flex: 1;
    }
    .card-solo-img {
      .screenMobile({
        flex: 3
      })
    }
    .card-solo-info {
      padding: 20px 30px 30px 30px;
      border: 0.05rem solid #CCCCCC;
    }
    .card-img {
      width: 100%;
      min-height: 100%;
    }
    .card-solo-title {
      font-size: 28px;
      margin-bottom: 17px;
    }
    .card-solo-description {
      margin-bottom: 72px;
      //min-height: 92px;
      color: #7A6A5E;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
@media (max-width: @screen-pc ) {
  .pc-page-body {
    .section  {
      .pc-scroll-wrap-inner {
        .card-content {
          height: 8rem;
          padding: 1rem;
        }
      }
    }
  }
}
@media (max-width: @screen-pad ) {
  .product-zone  {
    &.card-solo  {
      height: 250px;
      .card-solo-description {
        margin-bottom: 45px;
      }
    }
  }
}
@media (max-width: @screen-mobile ) {
  .product-zone  {
    &.card-solo  {
      height: 230px;
      .card-solo-description {
        margin-bottom: 10px;
      }
    }
  }
}
