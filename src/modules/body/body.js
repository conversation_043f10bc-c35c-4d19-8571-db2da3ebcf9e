// 页面 中间 body 部分 行为逻辑 主要是轮播图
let swiper1;
let swiper2;
let swiper6;

const resetSwiper = (innerWidth) => {
    const hasSwiper1 = document.querySelector('.pc-scroll-wrap-big');


    if (innerWidth > 1024) /* pc */ {
        if (hasSwiper1) {
            swiper1 = new Swiper('.pc-scroll-wrap-big', {
                direction: 'horizontal',
                loop: true,
                slidesPerView: 2,
                slidesPerGroup: 2,
                spaceBetween: 20,
                nextButton: '.slide-icon-right',
                prevButton: '.slide-icon-left',
            });
        }

        swiper2 = new Swiper('.pc-scroll-wrap', {
            direction: 'horizontal',
            loop: true,
            slidesPerView: 4,
            slidesPerGroup: 1,
            spaceBetween: 20,
            paginationType: "bullets",
            nextButton: '.swiper2-btn-right',
            prevButton: '.swiper2-btn-left',
        });


        swiper6 = new Swiper('.swiper6', {
            direction: 'horizontal',
            loop: true,
            slidesPerView: "auto",
            centeredSlidesBounds: true,
            spaceBetween: 20,
            centeredSlides: true
        });
    }

    if (innerWidth >= 768 && innerWidth < 1025) /* pad */ {
        if (hasSwiper1) {
            swiper1 = new Swiper('.pc-scroll-wrap-big', {
                direction: 'horizontal',
                loop: true,
                slidesPerView: 1,
                spaceBetween: 20,
                pagination: {
                    el: '.swiper1-pagination',
                    clickable: true
                },
                paginationType: "bullets",
            });
        }

        swiper2 = new Swiper('.pc-scroll-wrap', {
            direction: 'horizontal',
            loop: true,
            slidesPerView: 2,
            slidesPerGroup: 2,
            spaceBetween: 20,
            pagination: {
                el: '.swiper2-pagination',
                clickable: true
            },
        });

        swiper6 = new Swiper('.swiper6', {
            direction: 'horizontal',
            loop: true,
            slidesPerView: 2.3,
            spaceBetween: 20,
        });
    }

    if (innerWidth < 768) /* mobile */ {
        if (hasSwiper1) {
            swiper1 = new Swiper('.pc-scroll-wrap-big', {
                direction: 'horizontal',
                loop: true,
                slidesPerView: 1,
                spaceBetween: 20,
                pagination: {
                    el: '.swiper1-pagination',
                    clickable: true
                },
                paginationType: "bullets",
            });
        }

        swiper6 = new Swiper('.swiper6', {
            direction: 'horizontal',
            loop: true,
            slidesPerView: "auto",
            spaceBetween: 10,
        });
    }
};


window.onload = function () {

  // start 修复旅游推荐幻灯片小于5个时循环问题-----------start--------------------
  const $pcScrollWrap = $('.pc-scroll-wrap .swiper-wrapper');
  const $pcScrollWrapSlides = $pcScrollWrap.find('.swiper-slide');

  // 修复pc-scroll-wrap 等于5个临界值循环问题
  const slideCount = $pcScrollWrapSlides.length;
  if ( slideCount === 5) {
    const $pcScrollWrapSlidesCopy = $pcScrollWrapSlides.clone(true);
    $pcScrollWrap.append($pcScrollWrapSlidesCopy);
  }
  // 低于4个 不显示左右箭头
  const showNavigation = slideCount > 4;
  if (!showNavigation&&document.getElementById('swiper2LeftArrow')&&document.getElementById('swiper2RightArrow')) {
    document.getElementById('swiper2LeftArrow').style.display = 'none';
    document.getElementById('swiper2RightArrow').style.display = 'none';
  }
  // 修复旅游推荐幻灯片小于5个时循环问题-----------end--------------------

  // 修复活动推荐幻灯片小于5个时循环问题-----------start--------------------
    const $swiperWrapper = $('.swiper6 .swiper-wrapper');
    const $slides = $swiperWrapper.find('.swiper-slide');
    const eventSlideCount = $slides.length;

  // 修复活动推荐幻灯片小于5个时循环问题-----------end--------------------
  // 低于3个 不显示左右箭头,不循环
  const showEventNavigation = eventSlideCount > 2;
  if (!showEventNavigation&&document.getElementById('swiper6LeftArrow')&&document.getElementById('swiper6RightArrow')) {
    $swiperWrapper.closest('.swiper6').addClass('hide-before-after');
    document.getElementById('swiper6LeftArrow').style.display = 'none';
    document.getElementById('swiper6RightArrow').style.display = 'none';
  }
    if(showEventNavigation && $slides.length<6){
      const $slideCopy = $slides.clone(true);
      $swiperWrapper.append($slideCopy);
    }
  // 修复活动推荐幻灯片小于5个时循环问题-----------end--------------------

    resetSwiper(window.innerWidth);
};

function toPrev1() {
    swiper1.slidePrev();
}

function toNext1() {
    swiper1.slideNext();
}

function toPrev2() {
    swiper2.slidePrev();
}

function toNext2() {
    swiper2.slideNext();
}


function toSwiper6(flag) {
    if (flag > 0) {
        swiper6.slideNext();
    } else {
        swiper6.slidePrev();
    }
}

/**
 * 添加enter事件
 * 当轮播图
 */
window.addEventListener('keydown', (event) => {
    if (event.key !== "Enter") return;

    if (Object.is(swiper1LeftArrow, document.activeElement)) {
        swiper1RightArrow.focus();
    }
});

function throttle(func, wait) {
    let previous = 0;
    return function () {
        let now = Date.now(), context = this, args = [...arguments];
        if (now - previous > wait) {
            func.apply(context, args);
            previous = now; // 闭包，记录本次执行时间戳
        }
    };
}

window.addEventListener('resize', throttle(() => {
    if (swiper1) swiper1.destroy(true);
    if (swiper2) swiper2.destroy(true);
    if (swiper6) swiper6.destroy(true);
    const {innerWidth} = window;

    resetSwiper(innerWidth);
}, 300));

window.addEventListener('keydown', (event) => {

    if (event.key !== "Enter") return;

    if (Object.is(swiper1LeftArrow, document.activeElement)) {
        swiper1RightArrow.focus();
    }

    if (Object.is(swiper2LeftArrow, document.activeElement)) {
        swiper2RightArrow.focus();
    }

    if (Object.is(swiper6LeftArrow, document.activeElement)) {
        swiper6RightArrow.focus();
    }
});

// banner滚动
$(function () {
    var winWth = $(window).width();
    if (winWth < 768) {
        bannerRate(1.44, ".slide-banner");
    }
    if (768 <= winWth && winWth < 1025) {
        bannerRate(2.19, ".slide-banner");
    }
    if (1025 <= winWth && winWth < 1440) {
        bannerRate(3.01, ".slide-banner");
    }
    if (1440 <= winWth) {
        bannerRate(3.69, ".slide-banner");
    }

    //设置全屏banner宽高比
    function bannerRate(rate, cls) {
        var wth = $(window).width();
        if (wth > 1920) return;
        // debugger;
        if (wth < 1440) {
            var hgt = Math.floor(wth / rate);
            $(".slide-banner").css("height", hgt);
        } else {
            $(".slide-banner").removeAttr("style", "");
        }
    }

    $(window).resize(function () {
        var wthWidth = $(window).width();
        var scale = 0;
        if (wthWidth < 768) {
            scale = 1.44;
        }
        if (768 <= wthWidth && wthWidth < 1025) {
            scale = 2.19;
        }
        if(1025 <= wthWidth && wthWidth < 1440) {
            scale = 3.01;
        }
        if(wthWidth >= 1440) {
            scale = 3.69;
        }
        throttle(bannerRate(scale, ".slide-banner"), 1000);
    });

    function throttle(fn, delay) {
        var runFlag = false;
        return function (e) {
            // 判断之前的调用是否完成
            if (runFlag) {
                return false;
            }
            runFlag = true;
            setTimeout(function () {
                fn(e);
                runFlag = false;
            }, delay);
        };
    }
});

/**
 * 跳转url
 * @param url url
 */
function jumpToUrl (url) {
  window.location.href = url;
}

/**
 * 跳转url
 * @param url url
 */
function openUrl (url) {
  window.open(url);
}

