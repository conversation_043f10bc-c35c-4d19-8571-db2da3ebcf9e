@import '../../less/variables.less';

.module-step {
  height: 97px;
  display: flex;
  border: 1px solid @gray-2;
  background: @gray-0;

  &-left {
    flex: 1;
    margin: 20px;
    display: flex;
    align-items: end;
    justify-content: space-between;

    &-top {
      display: flex;
      align-items: center;
    }

    &-tag {
      border-radius: 8px;
      background: @sub-1;
      margin-right: 10px;
      padding: 0 10px;

      color: @sub-4;
      font-size: 14px;
    }

    &-text {
      display: flex;
      align-items: center;

      color: @gray-5;
      font-size: 20px;
    }

    &-way_icon {
      margin: 0 10px;
    }

    &-bottom {
      margin-top: 10px;
      display: flex;
      align-items: center;
    }

    &-person {
      color: @sub-4;
      font-size: 14px;
    }
  }

  &-right {
    flex: 1;
    display: flex;
    justify-content: space-evenly;

    &-step_img {
      width: 140px;
      height: 100%;
      margin-left: -26px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &-step_text {
      width: 20px;
      height: 20px;
      border-radius: 9999px;
      background: @gray-1;
      color: @gray-0;
      font-size: 16px;

      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        background: @gray-0;
        color: @brand-1;
      }
    }

    &-step_text1 {
      margin-top: 6px;
      font-size: 16px;
      color: @sub-2;

      &.active {
        color: @gray-0;
      }
    }
  }
}
