/**
 * 该文件专门处理tab切换时候的表单逻辑
 */

const __tabs = [
  document.getElementById('tab1'),
  document.getElementById('tab2'),
  document.getElementById('tab3'),
  document.getElementById('tab4'),
];

const __planes = [
  document.getElementById('plane1'),
  document.getElementById('plane2'),
  document.getElementById('plane3'),
  document.getElementById('plane4'),
];

const KEY_TYPE = {
  TAB: 'Tab',
  ENTER: 'Enter',
};

/**
 * 判断是否是结束元素
 * @param {HTMLElement} element
 */
const isEndElement = (element) => {
  return element.hasAttribute('data-tabEnd');
};

/**
 * 跳转到下一个tab
 * @type {null}
 */
let preFocusEle = null;

window.addEventListener('keyup', (event) => {
  if (!Object.is(event.key, KEY_TYPE.TAB)) return;

  if (preFocusEle && Object.is(preFocusEle, __tabs[0])) /* 第一个 tab 直接进入 */ {
    document.activeElement.blur();
    const starElement = __planes[0].querySelector('[data-tabBegin]');
    starElement.focus({preventScroll: false});
    preFocusEle = document.activeElement;
    return;
  }

  if (preFocusEle && isEndElement(preFocusEle)) {
    document.activeElement.blur();

    const tab = __tabs[currentTabIndex];
    tab.focus({preventScroll: false});
  }

  if (preFocusEle && Object.is(preFocusEle, __tabs[3])) {
    document.activeElement.blur();
    const overElement = document.querySelector('[data-tabOver]');
    overElement.focus({preventScroll: false});
  }

  preFocusEle = document.activeElement;
});

/**
 * 进入 from
 */
__tabs.forEach((tab, index) => {
  tab.addEventListener('keyup', (event) => {
    if (!Object.is(event.key, KEY_TYPE.ENTER)) return;

    const plane = __planes[index];
    const starElement = plane.querySelector('[data-tabBegin]');
    if (!starElement) return;

    starElement.focus({preventScroll: false});
    preFocusEle = starElement;
  });
});

