<div class="pc-form-tab-wrap" role="tab">
  <div class="tabs-wrap" role="tablist">
    <a
      href="javascript:"
      role="option"
      id="tab1"
      onclick="toggleIndex(1)"
      class="tab"
    >
      Flights
    </a>
    <a
      href="javascript:"
      role="option"
      id="tab2"
      onclick="toggleIndex(2)"
      class="tab"
    >
      Seat selection check-in
    </a>
    <a
      href="javascript:"
      role="option"
      id="tab3"
      onclick="toggleIndex(3)"
      class="tab"
    >
      My booking
    </a>
    <a
      href="javascript:"
      role="option"
      id="tab4"
      onclick="toggleIndex(4)"
      class="tab"
    >
      Flight Status
    </a>
  </div>

  <section role="tabpanel" aria-labelledby="tab1" class="plane" id="plane1">
    <div class="book-flights book-seat-check">
      <div
        aria-description="seat selection check-in"
        class="form-seat-check tab-target"
      >
        <div class="form-tab clearfix prompt-bubble-wrap">
          <div
            data-tabBegin
            class="no-style tab active"
            role="button"
            tabindex="0"
          >
            One way
          </div>
          <div class="no-style tab" role="button" tabindex="0">Round trip</div>
          <div class="no-style tab" role="button" tabindex="0">Multi-city</div>
          <div
            role="tooltip"
            tabindex="0"
            class="prompt-bubble tips"
            data-text="type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type"
          >
            <span class="icon-zh icon-zh-ask"></span>
          </div>
        </div>
        <div class="form-wrap">
          <div class="form-items active">
            <form class="form-table">
              <div class="form-wrap-col clearfix">
                <div class="row">
                  <div class="col-8 col-pad-12 seat-flex mb-box">
                    <div class="input-wrap city-origin from">
                      <div class="input-group input-single filled error">
                        <div class="label-wrap">
                          <label for="city-from2">From</label>
                        </div>
                        <input
                          type="text"
                          id="city-from2"
                          class="text city-component"
                          placeholder="Please select the departure"
                          value=""
                          data-citycode=""
                          aria-required="true"
                        />
                        <div class="error-tips">
                          <span class="icon-zh icon-zh-error"></span>
                          <span class="error-content"
                            >报错信息报错信息报错信息报错信息报错信息</span
                          >
                        </div>
                      </div>
                    </div>
                    <div class="input-wrap change-wrap">
                      <span
                        role="button"
                        class="icon-zh icon-zh-change-flight change"
                      ></span>
                    </div>
                    <div class="input-wrap city-origin to">
                      <div class="input-group input-single filled">
                        <div class="label-wrap">
                          <label for="city-to2">To</label>
                        </div>
                        <input
                          type="text"
                          id="city-to2"
                          class="text city-component"
                          placeholder="Please select the arrive"
                          value=""
                          data-citycode=""
                          aria-required="true"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="col-4 col-pad-12 calendar-group padding-l-1 pad-flex round-date"
                  >
                    <div class="col-6 pc-pr-rest">
                      <div class="input-wrap">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="single-date" class="title"
                              >Departure</label
                            >
                          </div>
                          <input
                            type="text"
                            class="text calendar-input depart-input calendar-flag bussiness-depart-input unchanged"
                            id="single-date"
                            placeholder="2024-05-01"
                            aria-required="true"
                            aria-label="calendar，please press enter to choose date"
                            data-date="2024-11-25"
                          />
                          <i class="icon-zh icon-zh-calendar calendar"></i>
                        </div>
                      </div>
                    </div>
                    <div class="col-6 pc-pl-rest">
                      <div class="input-wrap">
                        <div class="input-group input-single filled disabled">
                          <div class="label-wrap">
                            <label for="single-date" class="title"
                              >Return time</label
                            >
                          </div>
                          <input
                            type="text"
                            class="text calendar-input return-input calendar-flag disable bussiness-return-input"
                            id="single-date"
                            placeholder="2024-05-01"
                            aria-required="true"
                            aria-label="calendar，please press enter to choose date"
                            data-date="2024-11-25"
                            disabled
                          />
                          <i class="icon-zh icon-zh-calendar calendar"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-4 col-pad-6 mb-box pr">
                    <div class="input-wrap pc-limit">
                      <div
                        class="input-group input-single filled input-passenger"
                      >
                        <div class="label-wrap">
                          <label for="passengers1">Traveller</label>
                        </div>
                        <input
                          type="text"
                          id="passengers1"
                          class="text"
                          placeholder="0 Adults 0 Children 0 Infant"
                          value=""
                          aria-required="true"
                          readonly
                        />
                        <i class="icon-zh icon-zh-right-type1"></i>
                        <div class="passenger-count-wrap" id="passengerCount">
                          <div class="passenger-type">
                            <div class="type active" tabindex="0" role="button">
                              Passengers
                            </div>
                            <div class="type" tabindex="0" role="button">
                              Special travelers
                            </div>
                          </div>
                          <div class="passenger-ls">
                            <ul class="passenger-count-list show-this">
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-people"></span>
                                  <span class="ti">Adults</span>
                                  <span class="limit">12+ years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >1</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-children"></span>
                                  <span class="ti">Children</span>
                                  <span class="limit">2-11 years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="prompt-bubble-wrap">
                                <div
                                  class="name prompt-bubble prompt-bubble-l"
                                  tabindex="0"
                                  data-text="An infant must be acconmpanied by anadult passenger who is at least 18 yearsold.Please note that each adult can bringa maximum of one infant.If you are looking to bring more than one infant on board,please call 95361 for further information."
                                >
                                  <span class="icon-zh icon-zh-infant"></span>
                                  <span class="ti">Infant</span>
                                  <span class="limit">under 2 years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="btn-wrap">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-single"
                                  >Cancel</a
                                >
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-default"
                                  >Confirm</a
                                >
                              </li>
                            </ul>
                            <ul class="passenger-count-list others">
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-student"></span>
                                  <span class="ti">International student</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >1</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-seaman"></span>
                                  <span class="ti">Seaman</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-laborers"></span>
                                  <span class="ti">Laborers</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-migrate"></span>
                                  <span class="ti">Migrate</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="btn-wrap">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-single"
                                  >Cancel</a
                                >
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-default"
                                  >Confirm</a
                                >
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-8 col-pad-12 btn-flex">
                    <div class="input-wrap col-pad-12">
                      <div class="btn-box text-right">
                        <a
                          role="button"
                          href="javascript:"
                          aria-label="search button"
                          class="btn"
                          >Search</a
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="form-items multi-group pc">
            <form class="form-table">
              <div class="form-wrap-col clearfix">
                <div class="flight-wrapper">
                  <div class="row num-item">
                    <div class="flight-id">1</div>
                    <div class="col-8 col-pad-12 seat-flex mb-box">
                      <div class="input-wrap city-origin from">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="city-from3">From</label>
                          </div>
                          <input
                            type="text"
                            id="city-from3"
                            class="text city-component"
                            placeholder="Please select the departure"
                            value=""
                            data-citycode=""
                            aria-required="true"
                          />
                        </div>
                      </div>
                      <div class="input-wrap change-wrap">
                        <span
                          role="button"
                          class="icon-zh icon-zh-change-flight change"
                        ></span>
                      </div>
                      <div class="input-wrap city-origin to">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="city-to3">To</label>
                          </div>
                          <input
                            type="text"
                            id="city-to3"
                            class="text city-component"
                            placeholder="Please select the arrive"
                            value=""
                            data-citycode=""
                            aria-required="true"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="col-4 col-pad-6 pr padding-l-1">
                      <div class="input-wrap calendar-group">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="single-date" class="title"
                              >Departure</label
                            >
                          </div>
                          <input
                            type="text"
                            class="text calendar-input depart-input calendar-flag multi-calendar"
                            id="single-date"
                            placeholder="2024-05-01"
                            aria-required="true"
                            aria-label="calendar，please press enter to choose date"
                            data-date="2024-11-25"
                          />
                          <i class="icon-zh icon-zh-calendar calendar"></i>
                        </div>
                      </div>
                      <div
                        class="delete-flight-btn"
                        role="button"
                        aria-label="delete this flight segment"
                      >
                        <span class="icon-zh icon-zh-delete1"></span>
                      </div>
                    </div>
                  </div>
                  <div class="row num-item">
                    <div class="flight-id">2</div>
                    <div class="col-8 col-pad-12 seat-flex mb-box">
                      <div class="input-wrap city-origin from">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="city-from4">From</label>
                          </div>
                          <input
                            type="text"
                            id="city-from4"
                            class="text city-component"
                            placeholder="Please select the departure"
                            value=""
                            data-citycode=""
                            aria-required="true"
                          />
                        </div>
                      </div>
                      <div class="input-wrap change-wrap">
                        <span
                          role="button"
                          class="icon-zh icon-zh-change-flight change"
                        ></span>
                      </div>
                      <div class="input-wrap city-origin to">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="city-to4">To</label>
                          </div>
                          <input
                            type="text"
                            id="city-to4"
                            class="text city-component"
                            placeholder="Please select the arrive"
                            value=""
                            data-citycode=""
                            aria-required="true"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="col-4 col-pad-6 pr padding-l-1">
                      <div class="input-wrap calendar-group">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="single-date" class="title"
                              >Departure</label
                            >
                          </div>
                          <input
                            type="text"
                            class="text calendar-input depart-input calendar-flag multi-calendar"
                            id="single-date-sg2"
                            placeholder="2024-05-01"
                            aria-required="true"
                            aria-label="calendar，please press enter to choose date"
                            data-date="2024-11-25"
                          />
                          <i class="icon-zh icon-zh-calendar calendar"></i>
                        </div>
                      </div>
                      <div
                        class="delete-flight-btn"
                        role="button"
                        aria-label="delete this flight segment"
                      >
                        <span class="icon-zh icon-zh-delete1"></span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="add-wrap">
                    <a role="button" href="javascript:" class="button add-trip">
                      <span class="icon-zh icon-zh-add"></span>Add a trip
                    </a>
                  </div>
                </div>
                <div class="row pad-flex flex-r">
                  <div class="w30">
                    <div class="input-wrap">
                      <div
                        class="input-group input-single filled input-passenger"
                      >
                        <div class="label-wrap">
                          <label for="passengers2">Traveller</label>
                        </div>
                        <input
                          type="text"
                          id="passengers2"
                          class="text"
                          placeholder="0 Adults 0 Children 0 Infant"
                          value=""
                          aria-required="true"
                          readonly
                        />
                        <i class="icon-zh icon-zh-right-type1"></i>
                        <div class="passenger-count-wrap" id="passengerCount">
                          <div class="passenger-type">
                            <div class="type active" tabindex="0" role="button">
                              Passengers
                            </div>
                            <div class="type" tabindex="0" role="button">
                              Special travelers
                            </div>
                          </div>
                          <div class="passenger-ls">
                            <ul class="passenger-count-list show-this">
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-people"></span>
                                  <span class="ti">Adults</span>
                                  <span class="limit">12+ years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >1</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-children"></span>
                                  <span class="ti">Children</span>
                                  <span class="limit">2-11 years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="prompt-bubble-wrap">
                                <div
                                  class="name prompt-bubble prompt-bubble-l"
                                  tabindex="0"
                                  data-text="An infant must be acconmpanied by anadult passenger who is at least 18 yearsold.Please note that each adult can bringa maximum of one infant.If you are looking to bring more than one infant on board,please call 95361 for further information."
                                >
                                  <span class="icon-zh icon-zh-infant"></span>
                                  <span class="ti">Infant</span>
                                  <span class="limit">under 2 years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="btn-wrap">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-single"
                                  >Cancel</a
                                >
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-default"
                                  >Confirm</a
                                >
                              </li>
                            </ul>
                            <ul class="passenger-count-list others">
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-student"></span>
                                  <span class="ti">International student</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >1</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-seaman"></span>
                                  <span class="ti">Seaman</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-laborers"></span>
                                  <span class="ti">Laborers</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-migrate"></span>
                                  <span class="ti">Migrate</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="btn-wrap">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-single"
                                  >Cancel</a
                                >
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-default"
                                  >Confirm</a
                                >
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="w30">
                    <div class="input-wrap">
                      <div class="input-group input-single filled">
                        <div class="label-wrap">
                          <label for="Class">Class</label>
                        </div>
                        <input
                          type="text"
                          id="Class"
                          class="text select-text"
                          placeholder="Please fill in the input"
                          value="Economy class"
                          aria-required="true"
                          readonly
                        />
                        <i class="icon-zh icon-zh-right-type1"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row pad-rest-ml">
                  <div class="input-wrap col-pad-12">
                    <div class="btn-box text-right">
                      <a
                        role="button"
                        href="javascript:"
                        aria-label="Enquiry button"
                        class="btn"
                        >Enquiry</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section role="tabpanel" class="plane" id="plane2" aria-labelledby="tab2">
    <div class="book-seat-check">
      <div
        aria-description="seat selection check-in"
        class="form-seat-check tab-target"
      >
        <div class="form-tab clearfix">
          <div data-tabBegin class="no-style tab active" role="button">
            Seat selection check-in
          </div>
          <div class="no-style tab" role="button">Record Search</div>
        </div>
        <div class="form-wrap">
          <div class="form-items active">
            <form class="form-table">
              <div class="form-seat-flex">
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="“idCard"
                        >Please enter your ID card number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="idCard"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="lastName">Last name/Surnname</label>
                    </div>
                    <input
                      type="text"
                      id="lastName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="firstName">First Name</label>
                    </div>
                    <input
                      type="text"
                      id="firstName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="flightNo"
                        >Please input the flight number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="flightNo"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap calendar-group">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="single-date" class="title">Departure</label>
                    </div>
                    <input
                      type="text"
                      class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                      id="single-date"
                      placeholder="Please choose"
                      aria-required="true"
                      aria-label="calendar，please press enter to choose date"
                    />
                    <i class="icon-zh icon-zh-calendar calendar"></i>
                  </div>
                </div>
              </div>
              <div class="check-read">
                <label
                  class="checkbox-group is-select"
                  aria-checked="false"
                  role="checkbox"
                >
                  <span class="checkbox"
                    ><input type="checkbox" /><i
                      class="icon-zh icon-zh-success-yuan"
                    ></i
                  ></span>
                </label>
                <p>
                  Agree to use
                  <a href="javascript:" class="link"
                    >Online Check in Agreement and Notice for Passengers
                    Carrying Dangerous Goods</a
                  >
                </p>
              </div>
              <ul class="warm-tips">
                <li class="title">Gentle hint:</li>
                <li>
                  • The seat selection & check-in function is only for adults
                  and child passengers accompanied by adults; after seat
                  pre-selection, passengers with babies or applying for special
                  services should go to the manual check-in counter of Shenzhen
                  Airlines 2 hours before the flight departure to check in.
                </li>
                <li>
                  • Please select seats and check in at official channels of
                  Shenzhen Airlines.
                </li>
              </ul>
              <div class="btn-box text-right">
                <a
                  role="button"
                  data-tabEnd
                  href="javascript:"
                  aria-label="button Seat selection check-in"
                  class="btn"
                  >Seat selection check-in</a
                >
              </div>
            </form>
          </div>
          <div class="form-items">
            <form class="form-table">
              <div class="form-seat-flex">
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="“idCard"
                        >Please enter your ID card number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="idCard"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="firstName">First Name</label>
                    </div>
                    <input
                      type="text"
                      id="firstName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="lastName">Last name/Surnname</label>
                    </div>
                    <input
                      type="text"
                      id="lastName"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="flightNo"
                        >Please input the flight number</label
                      >
                    </div>
                    <input
                      type="text"
                      id="flightNo"
                      class="text"
                      placeholder="Please fill in the input"
                      title="Please fill in the input"
                      value=""
                      aria-required="true"
                    />
                  </div>
                </div>
                <div class="input-wrap calendar-group">
                  <div class="input-group input-single filled">
                    <div class="label-wrap">
                      <label for="single-date" class="title">Departure</label>
                    </div>
                    <input
                      type="text"
                      class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                      id="single-date"
                      placeholder="Please choose"
                      aria-required="true"
                      aria-label="calendar，please press enter to choose date"
                    />
                    <i class="icon-zh icon-zh-calendar calendar"></i>
                  </div>
                </div>
              </div>
              <div class="check-read">
                <label
                  class="checkbox-group is-select"
                  aria-checked="false"
                  role="checkbox"
                >
                  <span class="checkbox"
                    ><input type="checkbox" /><i
                      class="icon-zh icon-zh-success-yuan"
                    ></i
                  ></span>
                </label>
                <p>
                  Agree to use
                  <a href="javascript:" class="link"
                    >Online Check in Agreement and Notice for Passengers
                    Carrying Dangerous Goods</a
                  >
                </p>
              </div>
              <ul class="warm-tips">
                <li class="title">Gentle hint:</li>
                <li>
                  • The seat selection & check-in function is only for adults
                  and child passengers accompanied by adults; after seat
                  pre-selection, passengers with babies or applying for special
                  services should go to the manual check-in counter of Shenzhen
                  Airlines 2 hours before the flight departure to check in.
                </li>
                <li>
                  • Please select seats and check in at official channels of
                  Shenzhen Airlines.
                </li>
              </ul>
              <div class="btn-box text-right">
                <a href="javascript:" role="button" class="btn">query</a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section role="tabpanel" class="plane" id="plane3" aria-labelledby="ta3">
    <div class="book-plane-inner">
      <form
        role="form"
        aria-description="my booking form"
        class="form-items-wrap"
      >
        <div class="input-group input-single filled">
          <div class="label-wrap">
            <label for="3-1-input">Type</label>
          </div>
          <input
            type="text"
            id="3-1-input"
            data-tabBegin
            readonly
            onclick="openDropdown(this)"
            class="text"
            placeholder="Please select the departure"
            value="hello world1"
            aria-required="true"
            readonly
          />
          <i
            class="icon-zh icon-zh-right-type1 dropdown-icon"
            alt="select icon"
          ></i>
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-2-input">Order NO./TicketNO.</label>
          </div>

          <input
            id="3-2-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-3-input">Frist Name</label>
          </div>

          <input
            id="3-3-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>
        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-4-input">Last name/Surname</label>
          </div>

          <input
            id="3-4-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>
        <div class="input-group input-single">
          <div class="label-wrap">
            <label for="3-5-input">Please input the flight number</label>
          </div>

          <input
            id="3-5-input"
            aria-required="true"
            class="text"
            placeholder="please input"
            title="please input"
          />
        </div>
        <div
          class="depart-input input-group input-wrap calendar-group input-single"
          id="picker1"
        >
          <div class="label-wrap">
            <label for="3-6-input">Departure</label>
          </div>

          <input
            id="3-6-input"
            aria-required="true"
            class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
            placeholder="please choose"
          />
          <i class="icon-zh icon-zh-calendar calendar"></i>
        </div>
      </form>

      <div class="btn-box">
        <a
          role="button"
          data-tabEnd
          href="javascript:"
          aria-label="search button"
          class="submit-btn __main-button"
          >Search</a
        >
      </div>
    </div>
  </section>

  <section role="tabpanel" class="plane" id="plane4" aria-labelledby="tab4">
    <div class="status-plane-inner">
      <div class="inner-tab-wrap" role="radiogroup">
        <a
          href="javascript:"
          role="option"
          data-tabBegin
          id="plane4InnerBtn1"
          onclick="togglePlane4InnerTab(0)"
          onfocusin="togglePlane4InnerTab(0)"
          class="no-style inner-tab-item"
        >
          Buy route
        </a>
        <a
          href="javascript:"
          role="option"
          id="plane4InnerBtn2"
          onclick="togglePlane4InnerTab(1)"
          onfocusin="togglePlane4InnerTab(1)"
          class="no-style inner-tab-item"
        >
          By flight number
        </a>
      </div>

      <div id="tab1plane" class="plane-4-inner-plane">
        <form
          class="form-items-wrap form-items-wrap-icon"
          role="form"
          aria-description="Buy route form"
        >
          <div class="input-group input-single city-origin">
            <div class="label-wrap"><label for="city-from">From</label></div>
            <input
              type="text"
              id="flightTrendsSearchdepartAear"
              class="text city-component"
              placeholder="Please select the departure"
              value=""
              data-citycode=""
              aria-required="true"
            />
          </div>
          <span
            onclick="replacementValue('flightTrendsSearchdepartAear', 'flightTrendsSearchArriveArea')"
            class="cursor-pointer icon-repeatedly icon-zh icon-zh-change-flight change"
          >
          </span>
          <div class="input-group input-single city-origin">
            <div class="label-wrap">
              <label for="city-to">To</label>
            </div>

            <input
              aria-required="true"
              id="flightTrendsSearchArriveArea"
              class="text city-component"
              placeholder="please choose"
            />
          </div>

          <div class="input-group calendar-group input-single">
            <div class="label-wrap">
              <label for="4-1-3-input" class="form-item-label">Departure</label>
            </div>

            <input
              aria-required="true"
              id="4-1-3-input"
              class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
              placeholder="please choose"
            />
            <i class="icon-zh icon-zh-calendar calendar"></i>
          </div>
        </form>

        <div class="btn-box">
          <a
            role="button"
            data-tabEnd
            href="javascript:"
            aria-description="search button"
            aria-label="search button"
            class="submit-btn __main-button"
            >Search</a
          >
        </div>
      </div>

      <div id="tab2plane" class="plane-4-inner-plane">
        <form
          class="form-items-wrap form-items-wrap-start"
          role="form"
          aria-description="By flight number form"
        >
          <div class="input-group input-single">
            <div class="label-wrap">
              <label for="4-2-1-input" class="form-item-label"
                >Flight number</label
              >
            </div>

            <input
              aria-required="true"
              id="4-2-1-input"
              class="text"
              placeholder="please choose"
              title="please choose"
            />
          </div>

          <div class="input-group calendar-group input-single">
            <div class="label-wrap">
              <label for="4-2-2-input">Departure</label>
            </div>

            <input
              aria-required="true"
              id="4-2-2-input"
              class="text calendar-input depart-input calendar-input-disabled-before calendar-flag"
              placeholder="please choose"
            />
            <i class="icon-zh icon-zh-calendar calendar"></i>
          </div>
        </form>

        <div class="btn-box">
          <a
            href="javascript:"
            role="button"
            aria-description="search button"
            class="submit-btn __main-button"
          >
            Search
          </a>
        </div>
      </div>
    </div>
  </section>
</div>

<div class="mobile-from-tab-wrap">
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab1"
      onclick="toggleTab(0)"
      onfocus="toggleTab(0)"
    >
      <div class="tab-header-text">Flight</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <div class="book-flights book-seat-check">
        <div
          aria-description="seat selection check-in"
          class="form-seat-check tab-target"
        >
          <div class="form-tab clearfix prompt-bubble-wrap">
            <div class="tab active" role="button">One way</div>
            <div class="tab" role="button">Round trip</div>
            <div class="tab" role="button">Multi-city</div>
            <div
              role="tooltip"
              tabindex="0"
              class="prompt-bubble tips"
              data-text="type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type type"
            >
              <span class="icon-zh icon-zh-ask"></span>
            </div>
          </div>
          <div class="form-wrap">
            <div class="form-items active">
              <form class="form-table">
                <div class="form-wrap-col clearfix">
                  <div class="row">
                    <div class="col-mobile-11">
                      <div class="input-wrap city-origin from error">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="city-from6">From</label>
                          </div>
                          <input
                            type="text"
                            id="city-from6"
                            class="text city-component"
                            placeholder="Please select the departure"
                            value=""
                            data-citycode=""
                            aria-required="true"
                          />
                          <div class="error-tips">
                            <span class="icon-zh icon-zh-error"></span>
                            <span class="error-content"
                              >报错信息报错信息报错信息报错信息报错信息</span
                            >
                          </div>
                        </div>
                      </div>
                      <div class="input-wrap city-origin to">
                        <div class="input-group input-single filled">
                          <div class="label-wrap">
                            <label for="city-to6">To</label>
                          </div>
                          <input
                            type="text"
                            id="city-to6"
                            class="text city-component"
                            placeholder="Please select the arrive"
                            value=""
                            data-citycode=""
                            aria-required="true"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="col-mobile-1">
                      <div class="input-wrap change-wrap">
                        <span
                          role="button"
                          class="icon-zh icon-zh-change-flight change"
                        ></span>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-mobile-11 calendar-group round-date">
                      <div class="input-group input-single filled">
                        <div class="label-wrap">
                          <label for="single-date" class="title"
                            >Departure</label
                          >
                        </div>
                        <input
                          type="text"
                          class="text calendar-input depart-input calendar-flag bussiness-depart-input unchanged"
                          id="single-date"
                          placeholder="2024-05-01"
                          aria-required="true"
                          aria-label="calendar，please press enter to choose date"
                          data-date="2024-11-25"
                        />
                        <i class="icon-zh icon-zh-calendar calendar"></i>
                      </div>
                      <div class="input-group input-single filled disabled">
                        <div class="label-wrap">
                          <label for="single-date" class="title"
                            >Return time</label
                          >
                        </div>
                        <input
                          type="text"
                          class="text calendar-input return-input calendar-flag disable bussiness-return-input"
                          id="single-date"
                          placeholder="2024-05-01"
                          aria-required="true"
                          aria-label="calendar，please press enter to choose date"
                          data-date="2024-11-25"
                          disabled
                        />
                        <i class="icon-zh icon-zh-calendar calendar"></i>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-mobile-11">
                      <div
                        class="input-group input-single filled input-passenger"
                      >
                        <div class="label-wrap">
                          <label for="passengers2">Traveller</label>
                        </div>
                        <input
                          type="text"
                          id="passengers2"
                          class="text"
                          placeholder="0 Adults 0 Children 0 Infant"
                          value=""
                          aria-required="true"
                          readonly
                        />
                        <i class="icon-zh icon-zh-right-type1"></i>
                        <div class="passenger-count-wrap" id="passengerCount">
                          <div class="passenger-type">
                            <div class="type active" tabindex="0" role="button">
                              Passengers
                            </div>
                            <div class="type" tabindex="0" role="button">
                              Special travelers
                            </div>
                          </div>
                          <div class="passenger-ls">
                            <ul class="passenger-count-list show-this">
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-people"></span>
                                  <span class="ti">Adults</span>
                                  <span class="limit">12+ years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >1</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-children"></span>
                                  <span class="ti">Children</span>
                                  <span class="limit">2-11 years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="prompt-bubble-wrap">
                                <div
                                  class="name prompt-bubble prompt-bubble-l"
                                  tabindex="0"
                                  data-text="An infant must be acconmpanied by anadult passenger who is at least 18 yearsold.Please note that each adult can bringa maximum of one infant.If you are looking to bring more than one infant on board,please call 95361 for further information."
                                >
                                  <span class="icon-zh icon-zh-infant"></span>
                                  <span class="ti">Infant</span>
                                  <span class="limit">under 2 years</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="btn-wrap">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-single"
                                  >Cancel</a
                                >
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-default"
                                  >Confirm</a
                                >
                              </li>
                            </ul>
                            <ul class="passenger-count-list others">
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-student"></span>
                                  <span class="ti">International student</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >1</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-seaman"></span>
                                  <span class="ti">Seaman</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-laborers"></span>
                                  <span class="ti">Laborers</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li>
                                <div class="name" tabindex="0">
                                  <span class="icon-zh icon-zh-migrate"></span>
                                  <span class="ti">Migrate</span>
                                </div>
                                <div class="count-tools">
                                  <a
                                    href="javascript:"
                                    role="button"
                                    class="disabled"
                                  >
                                    <span
                                      class="icon-zh icon-zh-sub-sum disabled"
                                    ></span>
                                  </a>
                                  <span
                                    class="number"
                                    tabindex="0"
                                    id="market-adults-num"
                                    >0</span
                                  >
                                  <a href="javascript:" role="button">
                                    <span
                                      class="icon-zh icon-zh-add-sum"
                                    ></span>
                                  </a>
                                </div>
                              </li>
                              <li class="btn-wrap">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-single"
                                  >Cancel</a
                                >
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="btn-default"
                                  >Confirm</a
                                >
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-mobile-12">
                      <div class="btn-box text-right">
                        <a
                          role="button"
                          href="javascript:"
                          aria-label="search button"
                          class="btn"
                          >Search</a
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="form-items multi-group mobile">
              <form class="form-table">
                <div class="form-wrap-col clearfix">
                  <div class="flight-wrapper">
                    <div class="row num-item">
                      <div class="flight-id">1</div>
                      <div class="col-mobile-11">
                        <div class="input-wrap city-origin from">
                          <div class="input-group input-single filled">
                            <div class="label-wrap">
                              <label for="city-from7">From</label>
                            </div>
                            <input
                              type="text"
                              id="city-from7"
                              class="text city-component"
                              placeholder="Please select the departure"
                              value=""
                              data-citycode=""
                              aria-required="true"
                            />
                          </div>
                        </div>
                        <div class="input-wrap city-origin to">
                          <div class="input-group input-single filled">
                            <div class="label-wrap">
                              <label for="city-to7">To</label>
                            </div>
                            <input
                              type="text"
                              id="city-to7"
                              class="text city-component"
                              placeholder="Please select the arrive"
                              value=""
                              data-citycode=""
                              aria-required="true"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-mobile-1">
                        <div class="input-wrap change-wrap">
                          <span
                            role="button"
                            class="icon-zh icon-zh-change-flight change"
                          ></span>
                        </div>
                      </div>
                      <div class="col-mobile-11">
                        <div class="input-wrap calendar-group">
                          <div class="input-group input-single filled">
                            <div class="label-wrap">
                              <label for="single-date" class="title"
                                >Departure</label
                              >
                            </div>
                            <input
                              type="text"
                              class="text calendar-input depart-input calendar-flag"
                              id="single-date"
                              placeholder="2024-05-01"
                              aria-required="true"
                              aria-label="calendar，please press enter to choose date"
                              data-date="2024-11-25"
                            />
                            <i class="icon-zh icon-zh-calendar calendar"></i>
                          </div>
                        </div>
                      </div>
                      <div class="col-mobile-1">
                        <div
                          class="delete-flight-btn"
                          role="button"
                          aria-label="delete this flight segment"
                        >
                          <span class="icon-zh icon-zh-delete1"></span>
                        </div>
                      </div>
                    </div>
                    <div class="row num-item">
                      <div class="flight-id">2</div>
                      <div class="col-mobile-11">
                        <div class="input-wrap city-origin from">
                          <div class="input-group input-single filled">
                            <div class="label-wrap">
                              <label for="city-from8">From</label>
                            </div>
                            <input
                              type="text"
                              id="city-from8"
                              class="text city-component"
                              placeholder="Please select the departure"
                              value=""
                              data-citycode=""
                              aria-required="true"
                            />
                          </div>
                        </div>
                        <div class="input-wrap city-origin to">
                          <div class="input-group input-single filled">
                            <div class="label-wrap">
                              <label for="city-to8">To</label>
                            </div>
                            <input
                              type="text"
                              id="city-to8"
                              class="text city-component"
                              placeholder="Please select the arrive"
                              value=""
                              data-citycode=""
                              aria-required="true"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="col-mobile-1">
                        <div class="input-wrap change-wrap">
                          <span
                            role="button"
                            class="icon-zh icon-zh-change-flight change"
                          ></span>
                        </div>
                      </div>
                      <div class="col-mobile-11">
                        <div class="input-wrap calendar-group">
                          <div class="input-group input-single filled">
                            <div class="label-wrap">
                              <label for="single-date" class="title"
                                >Departure</label
                              >
                            </div>
                            <input
                              type="text"
                              class="text calendar-input depart-input calendar-flag"
                              id="single-date-mobile-sg2"
                              placeholder="2024-05-01"
                              aria-required="true"
                              aria-label="calendar，please press enter to choose date"
                              data-date="2024-11-25"
                            />
                            <i class="icon-zh icon-zh-calendar calendar"></i>
                          </div>
                        </div>
                      </div>
                      <div class="col-mobile-1">
                        <div
                          class="delete-flight-btn"
                          role="button"
                          aria-label="delete this flight segment"
                        >
                          <span class="icon-zh icon-zh-delete1"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="reset-btn-width">
                    <div class="add-wrap">
                      <a
                        role="button"
                        href="javascript:"
                        class="button add-trip"
                      >
                        <span class="icon-zh icon-zh-add"></span>Add a trip
                      </a>
                    </div>
                  </div>
                  <div class="reset-btn-width">
                    <div
                      class="input-group input-single filled input-passenger"
                    >
                      <div class="label-wrap">
                        <label for="passengers2">Traveller</label>
                      </div>
                      <input
                        type="text"
                        id="passengers2"
                        class="text"
                        placeholder="0 Adults 0 Children 0 Infant"
                        value=""
                        aria-required="true"
                        readonly
                      />
                      <i class="icon-zh icon-zh-right-type1"></i>
                      <div class="passenger-count-wrap" id="passengerCount">
                        <div class="passenger-type">
                          <div class="type active" tabindex="0" role="button">
                            Passengers
                          </div>
                          <div class="type" tabindex="0" role="button">
                            Special travelers
                          </div>
                        </div>
                        <div class="passenger-ls">
                          <ul class="passenger-count-list show-this">
                            <li>
                              <div class="name" tabindex="0">
                                <span class="icon-zh icon-zh-people"></span>
                                <span class="ti">Adults</span>
                                <span class="limit">12+ years</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >1</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li>
                              <div class="name" tabindex="0">
                                <span class="icon-zh icon-zh-children"></span>
                                <span class="ti">Children</span>
                                <span class="limit">2-11 years</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >0</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li class="prompt-bubble-wrap">
                              <div
                                class="name prompt-bubble prompt-bubble-l"
                                tabindex="0"
                                data-text="An infant must be acconmpanied by anadult passenger who is at least 18 yearsold.Please note that each adult can bringa maximum of one infant.If you are looking to bring more than one infant on board,please call 95361 for further information."
                              >
                                <span class="icon-zh icon-zh-infant"></span>
                                <span class="ti">Infant</span>
                                <span class="limit">under 2 years</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >0</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li class="btn-wrap">
                              <a
                                href="javascript:"
                                role="button"
                                class="btn-single"
                                >Cancel</a
                              >
                              <a
                                href="javascript:"
                                role="button"
                                class="btn-default"
                                >Confirm</a
                              >
                            </li>
                          </ul>
                          <ul class="passenger-count-list others">
                            <li>
                              <div class="name" tabindex="0">
                                <span class="icon-zh icon-zh-student"></span>
                                <span class="ti">International student</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >1</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li>
                              <div class="name" tabindex="0">
                                <span class="icon-zh icon-zh-seaman"></span>
                                <span class="ti">Seaman</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >0</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li>
                              <div class="name" tabindex="0">
                                <span class="icon-zh icon-zh-laborers"></span>
                                <span class="ti">Laborers</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >0</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li>
                              <div class="name" tabindex="0">
                                <span class="icon-zh icon-zh-migrate"></span>
                                <span class="ti">Migrate</span>
                              </div>
                              <div class="count-tools">
                                <a
                                  href="javascript:"
                                  role="button"
                                  class="disabled"
                                >
                                  <span
                                    class="icon-zh icon-zh-sub-sum disabled"
                                  ></span>
                                </a>
                                <span
                                  class="number"
                                  tabindex="0"
                                  id="market-adults-num"
                                  >0</span
                                >
                                <a href="javascript:" role="button">
                                  <span class="icon-zh icon-zh-add-sum"></span>
                                </a>
                              </div>
                            </li>
                            <li class="btn-wrap">
                              <a
                                href="javascript:"
                                role="button"
                                class="btn-single"
                                >Cancel</a
                              >
                              <a
                                href="javascript:"
                                role="button"
                                class="btn-default"
                                >Confirm</a
                              >
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="Class">Class</label>
                      </div>
                      <input
                        type="text"
                        id="Class"
                        class="text"
                        onfocus="openDropdown(this)"
                        placeholder="Please fill in the input"
                        value="hello world1"
                        aria-required="true"
                      />
                      <i
                        class="icon-zh icon-zh-right-type1 calendar dropdown-icon"
                      ></i>
                    </div>
                  </div>
                  <div class="reset-btn-width">
                    <div class="input-wrap">
                      <div class="btn-box text-right">
                        <a
                          role="button"
                          href="javascript:"
                          aria-label="Enquiry button"
                          class="btn"
                          >Enquiry</a
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab2"
      onclick="toggleTab(1)"
      onfocus="toggleTab(1)"
    >
      <div class="tab-header-text">Seat selection check-in</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <div class="book-seat-check">
        <div
          aria-description="seat selection check-in"
          class="form-seat-check tab-target"
        >
          <div class="form-tab clearfix">
            <div class="tab active" role="button">Seat selection check-in</div>
            <div class="tab" role="button">Record Search</div>
          </div>
          <div class="form-wrap">
            <div class="form-items active">
              <form class="form-table">
                <div class="form-seat-flex">
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="“idCard"
                          >Please enter your ID card number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="idCard"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="firstName">First Name</label>
                      </div>
                      <input
                        type="text"
                        id="firstName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="lastName">Last name/Surnname</label>
                      </div>
                      <input
                        type="text"
                        id="lastName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="flightNo"
                          >Please input the flight number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="flightNo"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap calendar-group">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="single-date" class="title">Departure</label>
                      </div>
                      <input
                        type="text"
                        class="text calendar-input depart-input calendar-flag"
                        id="single-date"
                        placeholder="2024-05-01"
                        aria-required="true"
                        aria-label="calendar，please press enter to choose date"
                      />
                      <i class="icon-zh icon-zh-calendar calendar"></i>
                    </div>
                  </div>
                </div>
                <div class="check-read">
                  <label
                    class="checkbox-group is-select"
                    aria-checked="false"
                    role="checkbox"
                  >
                    <span class="checkbox"
                      ><input type="checkbox" /><i
                        class="icon-zh icon-zh-success-yuan"
                      ></i
                    ></span>
                  </label>
                  <p>
                    Agree to use
                    <a href="javascript:" class="link"
                      >Online Check in Agreement and Notice for Passengers
                      Carrying Dangerous Goods</a
                    >
                  </p>
                </div>
                <ul class="warm-tips">
                  <li class="title">Gentle hint:</li>
                  <li>
                    • The seat selection & check-in function is only for adults
                    and child passengers accompanied by adults; after seat
                    pre-selection, passengers with babies or applying for
                    special services should go to the manual check-in counter of
                    Shenzhen Airlines 2 hours before the flight departure to
                    check in.
                  </li>
                  <li>
                    • Please select seats and check in at official channels of
                    Shenzhen Airlines.
                  </li>
                </ul>
                <div class="btn-box text-right">
                  <a href="javascript:" role="button" class="btn"
                    >Seat selection check-in</a
                  >
                </div>
              </form>
            </div>
            <div class="form-items">
              <form class="form-table">
                <div class="form-seat-flex">
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="“idCard"
                          >Please enter your ID card number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="idCard"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="firstName">First Name</label>
                      </div>
                      <input
                        type="text"
                        id="firstName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="lastName">Last name/Surnname</label>
                      </div>
                      <input
                        type="text"
                        id="lastName"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="flightNo"
                          >Please input the flight number</label
                        >
                      </div>
                      <input
                        type="text"
                        id="flightNo"
                        class="text"
                        placeholder="Please fill in the input"
                        value=""
                        aria-required="true"
                      />
                    </div>
                  </div>
                  <div class="input-wrap">
                    <div class="input-group input-single filled">
                      <div class="label-wrap">
                        <label for="single-date" class="title">Departure</label>
                      </div>
                      <input
                        type="text"
                        class="text calendar-input depart-input calendar-flag"
                        id="single-date"
                        placeholder="2024-05-01"
                        aria-required="true"
                        aria-label="calendar，please press enter to choose date"
                      />
                      <i class="icon-zh icon-zh-calendar calendar"></i>
                    </div>
                  </div>
                </div>
                <div class="check-read">
                  <label
                    class="checkbox-group is-select"
                    aria-checked="false"
                    role="checkbox"
                  >
                    <span class="checkbox"
                      ><input type="checkbox" /><i
                        class="icon-zh icon-zh-success-yuan"
                      ></i
                    ></span>
                  </label>
                  <p>
                    Agree to use
                    <a href="javascript:" class="link"
                      >Online Check in Agreement and Notice for Passengers
                      Carrying Dangerous Goods</a
                    >
                  </p>
                </div>
                <ul class="warm-tips">
                  <li class="title">Gentle hint:</li>
                  <li>
                    • The seat selection & check-in function is only for adults
                    and child passengers accompanied by adults; after seat
                    pre-selection, passengers with babies or applying for
                    special services should go to the manual check-in counter of
                    Shenzhen Airlines 2 hours before the flight departure to
                    check in.
                  </li>
                  <li>
                    • Please select seats and check in at official channels of
                    Shenzhen Airlines.
                  </li>
                </ul>
                <div class="btn-box text-right">
                  <a href="javascript:" role="button" class="btn">query</a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab3"
      onclick="toggleTab(2)"
      onfocus="toggleTab(2)"
    >
      <div class="tab-header-text">My booking</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <form>
        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Type</label>
          </div>

          <input
            onfocus="openDropdown(this)"
            class="text"
            placeholder="please choose"
            value="hello world1"
          />
          <i
            class="icon-zh icon-zh-right-type1 dropdown-icon"
            alt="select icon"
          ></i>
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Order NO./TicketNO.</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Frist Name</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Last name/Surname</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Please input the flight number</label>
          </div>

          <input disabled class="text" placeholder="please choose" />
        </div>

        <div class="input-group input-single">
          <div class="label-wrap">
            <label>Departure</label>
          </div>

          <input
            class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
            placeholder="please choose"
          />
          <i class="icon-zh icon-zh-calendar calendar"></i>
        </div>

        <div class="form-item form-item-end btn-box">
          <a
            role="button"
            href="javascript:"
            aria-label="search button"
            class="submit-btn __main-button"
            >Search</a
          >
        </div>
      </form>
    </div>
  </div>
  <div class="tab">
    <div
      class="tab-header"
      id="mobileTab4"
      onclick="toggleTab(3)"
      onfocus="toggleTab(3)"
    >
      <div class="tab-header-text">Flight Status</div>
      <i class="icon icon-zh icon-zh-add"></i>
    </div>
    <div class="tab-body">
      <div class="inner-tab-wrap">
        <div
          onfocusin="toggleMobilePlane4InnerTab(0)"
          id="mobilePlane4InnerBtn1"
          onclick="toggleMobilePlane4InnerTab(0)"
          class="inner-tab-item"
        >
          Buy route
        </div>
        <div
          onfocusin="toggleMobilePlane4InnerTab(1)"
          id="mobilePlane4InnerBtn2"
          onclick="toggleMobilePlane4InnerTab(1)"
          class="inner-tab-item"
        >
          By flight number
        </div>
      </div>

      <div id="mobileTab1plane" class="plane-4-inner-plane">
        <form>
          <div class="form-items-wrap">
            <div class="cb-wrap">
              <div class="cb-left">
                <div class="input-group input-single city-origin">
                  <div class="label-wrap">
                    <label for="mobileFrom">From</label>
                  </div>

                  <input
                    id="flightTrendsSearchdepartAear"
                    class="text city-component"
                    placeholder="please choose"
                  />
                </div>

                <div class="input-group input-single city-origin">
                  <div class="label-wrap">
                    <label for="mobileTo">To</label>
                  </div>

                  <input
                    id="flightTrendsSearchArriveArea"
                    class="text city-component"
                    placeholder="please choose"
                  />
                </div>
              </div>

              <span
                onclick="replacementValue('flightTrendsSearchdepartAear', 'flightTrendsSearchArriveArea')"
                class="cursor-pointer icon-repeatedly icon-zh icon-zh-change-flight change"
              >
              </span>
            </div>

            <div class="input-single input-group input-group-short">
              <div class="label-wrap">
                <label for="mobileTo">Departure</label>
              </div>

              <input
                id="mobileTo"
                class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                placeholder="please choose"
              />
              <i class="icon-zh icon-zh-calendar calendar"></i>
            </div>
          </div>

          <div class="btn-box">
            <a
              role="button"
              href="javascript:"
              aria-label="search button"
              class="submit-btn __main-button"
              >Search</a
            >
          </div>
        </form>
      </div>
      <div id="mobileTab2plane" class="plane-4-inner-plane">
        <form>
          <div class="form-items-wrap">
            <div class="input-group input-single">
              <div class="label-wrap">
                <label for="">Flight number</label>
              </div>

              <input class="text" placeholder="please choose" />
            </div>
            <div class="input-single input-group">
              <div class="label-wrap">
                <label for="">Departure</label>
              </div>

              <input
                class="text calendar-input calendar-input-disabled-before depart-input calendar-flag"
                placeholder="please choose"
              />
              <i class="icon-zh icon-zh-calendar calendar"></i>
            </div>

            <div class="form-item form-item-end btn-box">
              <a
                role="button"
                href="javascript:"
                aria-label="search button"
                class="submit-btn __main-button"
                >Search</a
              >
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
