@font-face {
  font-family: "icon-zh"; /* Project id 4735362 */
  src: url('fonts/iconfont.eot?t=1734486934533'); /* IE9 */
  src: url('fonts/iconfont.eot?t=1734486934533#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('fonts/iconfont.woff?t=1734486934533') format('woff'),
       url('fonts/iconfont.ttf?t=1734486934533') format('truetype');
}

.icon-zh {
  font-family: "icon-zh" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-zh-success:before {
  content: "\e630";
}

.icon-zh-refresh:before {
  content: "\e631";
}

.icon-zh-error:before {
  content: "\e62f";
}

.icon-zh-delete1:before {
  content: "\e62e";
}

.icon-zh-change-flight:before {
  content: "\e629";
}

.icon-zh-play:before {
  content: "\e803";
}

.icon-zh-ask:before {
  content: "\e62d";
}

.icon-zh-left:before {
  content: "\e62a";
}

.icon-zh-pause:before {
  content: "\e62b";
}

.icon-zh-right:before {
  content: "\e62c";
}

.icon-zh-turn-success:before {
  content: "\e61e";
}

.icon-zh-tip-info:before {
  content: "\e61f";
}

.icon-zh-turn-left:before {
  content: "\e628";
}

.icon-zh-children:before {
  content: "\e622";
}

.icon-zh-student:before {
  content: "\e623";
}

.icon-zh-migrate:before {
  content: "\e624";
}

.icon-zh-seaman:before {
  content: "\e625";
}

.icon-zh-infant:before {
  content: "\e626";
}

.icon-zh-laborers:before {
  content: "\e627";
}

.icon-zh-right-type1:before {
  content: "\e61c";
}

.icon-zh-flight-other:before {
  content: "\e61d";
}

.icon-zh-close:before {
  content: "\e620";
}

.icon-zh-people:before {
  content: "\e616";
}

.icon-zh-sub-sum:before {
  content: "\e617";
}

.icon-zh-add-sum:before {
  content: "\e618";
}

.icon-zh-delete:before {
  content: "\e61a";
}

.icon-zh-add:before {
  content: "\e61b";
}

.icon-zh-right-filling:before {
  content: "\e611";
}

.icon-zh-voice:before {
  content: "\e612";
}

.icon-zh-success-yuan:before {
  content: "\e613";
}

.icon-zh-question:before {
  content: "\e614";
}

.icon-zh-exchange:before {
  content: "\e609";
}

.icon-zh-menu:before {
  content: "\e60a";
}

.icon-zh-equalizer:before {
  content: "\e60b";
}

.icon-zh-profile:before {
  content: "\e60c";
}

.icon-zh-calendar:before {
  content: "\e60d";
}

.icon-zh-earth:before {
  content: "\e60e";
}

.icon-zh-chat:before {
  content: "\e60f";
}

.icon-zh-flight:before {
  content: "\e610";
}

