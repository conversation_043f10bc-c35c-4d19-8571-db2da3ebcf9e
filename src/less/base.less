.link {
  cursor: pointer;
  color: @picker-color-010;
  text-decoration: underline;
  &:hover {
    color: #942531;
  }
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.dn {
  display: none !important;
}

.db {
  display: block !important;
}

// pc隐藏
.pad-show {
  display: none;
}

.padding-l-1 {
  padding-left: 1rem;
}

/* 一般用于去除tab索引用途的 button / a 元素样式 */
.no-style {
  border     : none;
  background : transparent;
  cursor     : pointer;
  padding    : 0;
  margin     : 0;
}

/* 一般用于索引一些无法索引到的元素 */
.tab-a {
  position : absolute;
  top      : -1px;
  left     : -1px;
  bottom   : -1px;
  right    : -1px;
  z-index: 1;
}

.ml-2 {
  margin-left : 2rem;
}


@media (max-width : @screen-md-max) {
  .pad-hide { // pad隐藏，pc显示
    display: none;
  }
  .pad-show { // pad显示
    display: block;
  }
}

.singe-line {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}

.double-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.three-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.four-line {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}