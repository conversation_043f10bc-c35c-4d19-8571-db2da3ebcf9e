@import "variables";
@import "clearfix";


.row {
  --gutter-x   : 1rem;
  --gutter-y   : 0;

  display      : flex;
  flex-wrap    : wrap;
  margin-top   : calc(-1 * var(--gutter-y));
  margin-right : calc(-0.5 * var(--gutter-x));
  margin-left  : calc(-0.5 * var(--gutter-x));

  & > * {
    box-sizing    : border-box;
    flex-shrink   : 0;
    width         : 100%;
    max-width     : 100%;
    margin-top    : var(--gutter-y);
    padding-right : calc(var(--gutter-x) * 0.5);
    padding-left  : calc(var(--gutter-x) * 0.5);
  }
}

.container {
  padding-left: @grid-gutter-width;
  padding-right: @grid-gutter-width;
  width: auto;

    @media only screen and (max-width : 767px) {
      padding-left: 15px;
      padding-right: 15px;
    }
    
    @media only screen and (min-width : @screen-pc) {
      margin: 0 auto;
      width : 72rem;
    }
  }


/* 间距 */
.make-grid-gap(@type) {
  .gap(@index) when (@index <= 6) {

    @selector-no-type : ~".gap-@{index}";
    @selector         : ~".gap-@{type}-@{index}";

    @{selector-no-type} {
      @gap       : @index * 0.25;
      --gutter-x : ~"@{gap}rem";
      --gutter-y : ~"@{gap}rem";
    }
    @{selector} {
      @gap       : @index * 0.25;
      --gutter-x : ~"@{gap}rem";
      --gutter-y : ~"@{gap}rem";
    }
    .gap(@index+1);
  }

  .gap(0);
}


.make-grid-columns() {
  .col(@index) {
    @selector : ~".col-mobile-@{index}, .col-pad-@{index}, .col-sPC-@{index}, .col-PC-@{index}";
    .col(@index+1, @selector);
  }

  .col(@index, @list) when (@index <= @grid-columns) {
    // 变量拼接
    @selector : ~"@{list}, .col-mobile-@{index}, .col-pad-@{index}, .col-sPC-@{index}, .col-PC-@{index}";
    .col(@index+1, @selector);
  }

  .col(@index, @list) when (@index > @grid-columns) {
    @{list} {
      flex : 0 0 auto;
    }
  }
  .col(1);
}

.make-grid-columns();

.make-column-width(@type) {
  .col(@index) when (@index <= @grid-columns) {
    @selector : ~".col-@{type}-@{index}";

    @{selector} {
      position: relative;
      @w    : @index * (1/@grid-columns) * 100;
      width : ~"@{w}%" !important;
      float: left;
    }
    // 递归调用
    .col(@index+1);
  }
  .col(1);
}

.make-column() {
  .col(@index) when (@index <= @grid-columns) {
    // 拼接
    @selector-no-type : ~".col-@{index}";

    @{selector-no-type} {
      position: relative;
      @w    : @index * (1/@grid-columns) * 100;
      width : ~"@{w}%";
      float: left;
    }
    // 递归调用
    .col(@index+1);
  }
  .col(1);
}

.make-column();

.make-column-offset(@type) {
  .col(@index) when (@index <= @grid-columns) {
    // 拼接
    @selector : ~".col-@{type}-offset-@{index}"; // col-xs-1
    @{selector} {
      @left       : @index * (1/@grid-columns) * 100;
      margin-left : ~"@{left}%";
    }
    // 递归调用
    .col(@index+1);
  }
  .col(1);
}


@media only screen and (max-width : @screen-xs-max) /* mobile */ {
  .make-grid-gap(mobile);
  .make-column-width(mobile);
  .make-column-offset(mobile);
}

@media only screen and (min-width : @screen-mobile) and (max-width : @screen-md-max) /* pad */ {
  .make-grid-gap(pad);
  .make-column-width(pad);
  .make-column-offset(pad);
}

@media only screen and (min-width : @screen-pad) and (max-width : @screen-pc) /* sPC */ {
  .make-grid-gap(sPC);
  .make-column-width(sPC);
  .make-column-offset(sPC);
}

@media only screen and (min-width : @screen-pc) /* PC */ {
  .make-grid-gap(PC);
  .make-column-width(PC);
  .make-column-offset(PC);
}

.single-line {
  text-overflow : ellipsis;
  overflow      : hidden;
  word-break    : break-all;
  white-space   : nowrap;
}
