@import '../../../less/variables.less';

.seven-day-calendar {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    font-size: 28px;
    font-weight: 500;
    color: @gray-5;

    &-text {
      display: flex;
      align-items: center;
      font-weight: 400;
    }

    &-i {
      margin-right: 6px;

      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 9999px;

      box-sizing: border-box;
      border: 2px solid @brand-1;
    }
  }

  &-body {
    margin-top: 20px;
    margin-bottom: 20px;

    .schedule-container {
      display: flex;
      flex-direction: column;
      border: 1px solid #ccc;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

      .header-row,
      .data-row {
        display: flex;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }
      }

      .corner-cell,
      .outbound-cell {
        flex-basis: 150px; // Fixed width for outbound column
        flex-shrink: 0;
        padding: 10px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        border-right: 1px solid #eee;
        background-color: @sub-1;
      }

      .inbound-headers {
        background-color: @sub-1;
      }

      .inbound-headers,
      .inbound-data {
        display: flex;
        flex-grow: 1; // Allows inbound columns to take remaining space
      }

      .inbound-date,
      .inbound-value {
        flex: 1; // Distributes space evenly among inbound cells
        padding: 10px;
        text-align: center;
        border-right: 1px solid #eee;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:last-child {
          border-right: none;
        }
      }

      .red-dot {
        margin-top: 10px;

        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 9999px;

        box-sizing: border-box;
        border: 2px solid @brand-1;
      }

      .outbound-date {
        span {
          font-size: 16px;
          color: @gray-4;
        }
      }

      .inbound-date {
        span {
          font-size: 16px;
          color: @gray-4;
          .black-bg & {
            // Nested style for span inside black-bg
            color: #ccc;
          }
        }
      }
    }
  }

  &-footer {
    font-size: 20px;
    color: @gray-5;
  }
}
