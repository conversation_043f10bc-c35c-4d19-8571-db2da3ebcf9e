<div class="seven-day-calendar">
  <div class="seven-day-calendar-header">
    <span>Select another date</span>
    <span class="seven-day-calendar-header-text">
      <i class="seven-day-calendar-header-i"></i>
      Lowest price
    </span>
  </div>

  <div class="seven-day-calendar-body">
    <div class="schedule-container">
      <div class="header-row">
        <div class="corner-cell">INBOUND<br />OUTBOUND</div>
        <div class="inbound-headers">
          <div class="inbound-date">06-22<br /><span>Sun</span></div>
          <div class="inbound-date">06-23<br /><span>Mon</span></div>
          <div class="inbound-date">06-24<br /><span>Tue</span></div>
          <div class="inbound-date">06-25<br /><span>Web</span></div>
          <div class="inbound-date">06-26<br /><span>Thu</span></div>
          <div class="inbound-date">06-27<br /><span>Fri</span></div>
          <div class="inbound-date">06-28<br /><span>Sat</span></div>
        </div>
      </div>
      <% data.sevenDayCalendarData.forEach(row => { %>
      <div class="data-row">
        <div class="outbound-cell">
          <div class="outbound-date">
            <%= row.outbound %>
            <br />
            <span><%= row.outboundDay %></span>
          </div>
        </div>
        <div class="inbound-data">
          <div class="inbound-value"><%= row.inbound["06-22"] %></div>
          <div class="inbound-value"><%= row.inbound["06-23"] %></div>
          <div class="inbound-value"><%= row.inbound["06-24"] %></div>
          <div class="inbound-value"><%= row.inbound["06-25"] %></div>
          <div class="inbound-value">
            <%= row.inbound["06-26"] %> <% if (row.inbound["06-26"] !== '-') {
            %><span class="red-dot"></span><% } %>
          </div>
          <div class="inbound-value">
            <%= row.inbound["06-27"] %> <% if (row.inbound["06-27"] !== '-') {
            %><span class="red-dot"></span><% } %>
          </div>
          <div class="inbound-value">
            <%= row.inbound["06-28"] %> <% if (row.inbound["06-28"] !== '-') {
            %><span class="red-dot"></span><% } %>
          </div>
        </div>
      </div>
      <% }) %>
    </div>
  </div>

  <p class="seven-day-calendar-footer">币种：CNY</p>
</div>
