@import './horizontalPriceCalendar/index.less';
@import './sevenDayCalendar/index.less';
@import './flightAndBrand/index.less';

// 航班选择页面样式
.fo {
  min-height: 100vh;

  &-banner {
    width: 100%;
    position: relative;
    z-index: -1;
  }

  &-container {
    max-width: 1380px;
    margin: -80px auto 136px;

    .wrap-horizontal-price-calendar {
      margin-top: 30px;
    }

    .wrap-seven-day-calendar {
      margin-top: 30px;
    }

    .wrap-flight-and-brand {
      margin-top: 30px;
    }
  }
}
