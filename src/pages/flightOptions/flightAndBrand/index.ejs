<div class="flight-and-brand">
  <div class="flight-and-brand-sift">
    <div class="flight-and-brand-sift-item __text-button">
      <span>Time</span>
      <img
        class="flight-and-brand-sift-item-img"
        src="../../../images/flightOptions/sort.png"
        alt=""
        srcset=""
      />
    </div>
    <div class="flight-and-brand-sift-item __text-button">
      <span>Price</span>
      <img
        class="flight-and-brand-sift-item-img"
        src="../../../images/flightOptions/sort.png"
        alt=""
        srcset=""
      />
    </div>
  </div>

  <div class="flight-and-brand-list">
    <div class="flight-container">
      <div class="flight-summary">
        <div class="flight-time-route">
          <div class="time"><%= data.flightData.departureTime %></div>
          <div class="airport"><%= data.flightData.departureAirport %></div>
          <div class="route-line">
            <span class="duration"><%= data.flightData.duration %></span>
            <i class="fas fa-plane"></i>
          </div>
          <div class="time"><%= data.flightData.arrivalTime %></div>
          <div class="airport"><%= data.flightData.arrivalAirport %></div>
        </div>
        <div class="flight-details">
          <div class="flight-info">
            <span><%= data.flightData.flightNumber %></span>
            <span><%= data.flightData.aircraft %></span>
            <% if (data.flightData.sharing) { %>
            <span class="shared">共享</span>
            <% } %> <% if (data.flightData.mealAvailable) { %>
            <i class="fas fa-utensils meal-icon"></i>
            <% } %>
          </div>
          <a
            href="<%= data.flightData.flightDetailsLink %>"
            class="flight-details-link"
            >Flight details <i class="fas fa-chevron-right"></i
          ></a>
        </div>
        <div class="price-options">
          <% data.flightData.priceOptions.forEach((option, index) => { %>
          <div class="price-option <%= index === 0 ? 'active' : '' %>">
            <div class="class-name">
              <i class="fas fa-plane-departure"></i> <%= option.class %>
            </div>
            <div class="price-cny">CNY</div>
            <div class="price"><%= option.price %></div>
            <div class="upwards">upwards</div>
          </div>
          <% }); %>
        </div>
      </div>

      <div class="booking-classes">
        <% data.flightData.bookingClasses.forEach(bookingClass => { %>
        <div class="class-card">
          <div class="card-header">
            <span class="remaining"
              ><%= bookingClass.remaining %> Remaining</span
            >
            <div class="class-name-large"><%= bookingClass.name %></div>
          </div>
          <div class="card-body">
            <div class="price-display">
              CNY <span class="price-value"><%= bookingClass.price %></span>
            </div>
            <div class="detail-row">
              <span>Taxes and Fees</span>
              <span>CNY <%= bookingClass.taxesAndFees %></span>
            </div>
            <div class="detail-row">
              <span>Fare</span> <span>CNY <%= bookingClass.fare %></span>
            </div>
            <div class="detail-row">
              <i class="fas fa-luggage-cart icon-margin-right"></i>
              <span>Cabin Class</span>
              <span><%= bookingClass.cabinClass %></span>
            </div>
            <div class="detail-row">
              <i class="fas fa-suitcase-rolling icon-margin-right"></i>
              <span>Baggage</span>
              <span class="highlight"><%= bookingClass.baggage %></span>
            </div>
            <div class="detail-row">
              <i class="fas fa-exchange-alt icon-margin-right"></i>
              <span>Refund</span>
              <span class="highlight"><%= bookingClass.refund %></span>
            </div>
            <div class="detail-row">
              <i class="fas fa-undo-alt icon-margin-right"></i>
              <span>Change</span>
              <span class="highlight"><%= bookingClass.change %></span>
            </div>
            <div class="detail-row">
              <i class="fas fa-star icon-margin-right"></i>
              <span>Base Mileage</span>
              <span class="highlight"><%= bookingClass.baseMileage %></span>
            </div>
            <div class="detail-row">
              <i class="fas fa-star icon-margin-right"></i>
              <span>Bonus Mileage</span>
              <span class="highlight"><%= bookingClass.bonusMileage %></span>
            </div>
          </div>
          <div class="card-footer">
            <button class="book-button">
              <i class="fas fa-book"></i> BOOK
            </button>
          </div>
        </div>
        <% }); %>
      </div>
    </div>
  </div>
</div>
