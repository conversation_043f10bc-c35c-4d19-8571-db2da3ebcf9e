@import '../../../less/variables.less';

.flight-and-brand {
  &-sift {
    display: flex;
    align-items: center;

    &-item {
      display: flex;
      align-items: center;
      margin-right: 20px;

      font-size: 16px;
      color: @gray-5;

      &-img {
        margin-left: 10px;
        width: 16px;
        height: 16px;
      }
    }
  }

  &-list {
    margin-top: 30px;

    .flight-container {
      margin: 0 auto;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .flight-summary {
      display: flex;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid;

      .flight-time-route {
        display: flex;
        align-items: center;
        flex: 2;
        padding-right: 20px;

        .time {
          font-size: 2em;
          font-weight: bold;
        }

        .airport {
          font-size: 0.9em;
          color: #777;
          margin: 0 10px;
          max-width: 150px; // To prevent long names from breaking layout
          text-align: center;
        }

        .route-line {
          flex-grow: 1;
          display: flex;
          align-items: center;
          position: relative;
          margin: 0 20px;

          &::before {
            content: '';
            flex-grow: 1;
            height: 1px;
            background-color: ;
            position: absolute;
            width: 100%;
          }

          .duration {
            background-color: #fff;
            padding: 0 8px;
            border: 1px solid;
            border-radius: 15px;
            font-size: 0.9em;
            color: #555;
            z-index: 1;
            position: relative;
          }

          .fa-plane {
            background-color: #fff;
            padding: 0 5px;
            color: #777;
            z-index: 1;
          }
        }
      }

      .flight-details {
        flex: 1.5;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        font-size: 0.9em;
        color: #555;
        margin-left: 20px;
        padding-right: 20px;
        border-right: 1px dashed;

        .flight-info {
          span {
            margin-right: 10px;
          }
          .shared {
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
          }
          .meal-icon {
            color: #888;
            margin-left: 5px;
          }
        }

        .flight-details-link {
          color: #007bff;
          text-decoration: none;
          margin-top: 10px;

          &:hover {
            text-decoration: underline;
          }
          .fa-chevron-right {
            font-size: 0.8em;
          }
        }
      }

      .price-options {
        flex: 1;
        display: flex;
        margin-left: 20px;

        .price-option {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 10px 5px;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;

          &.active {
            background-color: @brand-1;
            color: #fff;
            .price {
              color: #fff; // Keep red price white in active state
            }
          }
          &:nth-child(2) {
            // Premium Economy
            background-color: #f7ede2; // Light tan/gold
            color: @brand-1;
          }
          &:nth-child(3) {
            // Business Class
            background-color: @brand-1;
            color: #fff;
          }

          .class-name {
            font-size: 0.9em;
            margin-bottom: 5px;
            .fa-plane-departure {
              margin-right: 5px;
              font-size: 0.8em;
            }
          }
          .price-cny {
            font-size: 0.8em;
            color: #777;
            .active & {
              color: #fff;
            }
          }
          .price {
            font-size: 1.5em;
            font-weight: bold;
            color: @brand-1;
            line-height: 1;
          }
          .upwards {
            font-size: 0.7em;
            color: #777;
            .active & {
              color: #fff;
            }
          }
        }
      }
    }

    .booking-classes {
      display: grid;
      grid-template-columns: repeat(
        auto-fit,
        minmax(280px, 1fr)
      ); // Responsive columns
      gap: 20px;
      padding: 20px;
      background-color: #f8fafd;
    }

    .class-card {
      background-color: #fff;
      border: 1px solid;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .card-header {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="50" viewBox="0 0 100 50"><path fill="%23f9e9cc" d="M0 0h100v50H0z"/><path fill="%23f0d4a9" d="M0 50L50 0H0z"/></svg>'); /* Placeholder for a subtle pattern */
        background-repeat: repeat-x;
        background-size: 50px auto; // Adjust pattern size
        padding: 15px;
        text-align: center;
        position: relative;
        overflow: hidden; // To contain pseudo-elements if any
        border-bottom: 1px solid;

        .remaining {
          background-color: @brand-1;
          color: #fff;
          font-size: 0.8em;
          padding: 3px 8px;
          border-radius: 4px;
          position: absolute;
          top: 10px;
          left: 10px;
        }

        .class-name-large {
          font-size: 1.1em;
          font-weight: bold;
          margin-top: 10px;
          color: @brand-1;
        }
      }

      .card-body {
        padding: 15px;

        .price-display {
          text-align: center;
          font-size: 1.2em;
          font-weight: bold;
          margin-bottom: 15px;

          .price-value {
            font-size: 2em;
            color: @brand-1;
            margin-left: 5px;
          }
        }

        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px dashed @brand-1;
          font-size: 0.9em;
          color: #555;

          &:last-child {
            border-bottom: none;
          }

          .icon-margin-right {
            margin-right: 8px;
            color: #999;
          }

          .highlight {
            color: @brand-1;
            font-weight: bold;
          }
        }
      }

      .card-footer {
        padding: 15px;
        border-top: 1px solid;
        text-align: center;
        background-color: @brand-1;

        .book-button {
          background-color: @brand-1;
          color: #fff;
          border: none;
          padding: 12px 30px;
          border-radius: 5px;
          cursor: pointer;
          font-size: 1em;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;

          &:hover {
            background-color: darken(@brand-1, 10%);
          }

          .fa-book {
            margin-right: 8px;
          }
        }
      }
    }
  }
}
