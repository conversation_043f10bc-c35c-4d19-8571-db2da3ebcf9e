@import '../../../less/variables.less';

.horizontal-price-calendar {
  display: flex;

  &-left {
    flex: 1;
    display: flex;
    align-items: center;

    &-content {
      flex: 1;
      padding: 0 18px;
      display: flex;
    }
  }

  &-lr_btn {
    border-radius: 0;

    &-img {
      width: 15px;
    }

    &.disabled {
      cursor: not-allowed;
      background: @gray-1;
    }
  }

  &-right {
    margin-left: 18px;

    display: flex;
    flex-direction: column;
    align-items: center;

    &-img {
      width: 24px;
    }

    &-text {
      margin-top: 4px;
      font-size: 16px;
      color: @sub-3;
    }
  }

  &-wrap_item {
    flex: 1;
    border-left: 1px solid @sub-2;
    padding: 0 6px;

    &-no_bl {
      border-left: none;
    }
  }

  &-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    padding-top: 8px;
    padding-bottom: 8px;
    border-radius: 8px;
    border: 1px solid transparent;

    &-unit {
      font-size: 16px;
      color: @gray-5;
    }

    &-amount {
      font-size: 20px;
      color: @gray-5;
    }

    &-date,
    &-week {
      font-size: 16px;
      color: @gray-3;
    }

    &:hover {
      border: 1px solid @brand-1;
    }

    &.active {
      border: 1px solid @brand-1;

      & span {
        color: @brand-1 !important;
      }
    }
  }
}
