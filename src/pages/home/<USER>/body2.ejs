<div class="pc-page-body container" role="main">

    <!-- Proeudct Zone轮播 三端-->
    <section class="section">
        <div class="section-title">Product Zone</div>

        <div class="content row gap-0">
            <div class="info-card col-PC-6 col-sPC-6 col-pad-12 col-mobile-12">
                <div class="info-card-title single-line">
                    Product Zone
                </div>

                <p class="text">
                    Make the perfect holiday trip with the best offers and explore the b <br>
                    life experience bestMake the perfect holiday trip with the best <br>
                    lake the perfect holiday trip with the best offers and explore the b life experience <br>
                    bestMake the perfect holiday trip with the best
                </p>

                <div class="more">
                    Learn more
                    <img src="./../../images/middleImg/icon-arrow-left.svg" alt="learn more">
                </div>
            </div>

            <img alt="Product Zone"
                 class="card-img col-PC-6 col-sPC-6 col-pad-12 col-mobile-12"
                 src="./../../images/middleImg/star-alliance-img1.jpg">
        </div>
    </section>


   <!-- Travel Recommendations轮播 PC和pad端一套，手机端单独一套-->
   <section class="section">
    <!-- Travel Recommendations轮播 PC和pad端-->
    <div class="section-title">Travel Recommendations</div>

    <div class="swiper pc-scroll-wrap">
        <img role="button" alt="previous"  id="swiper2LeftArrow"  tabindex="240"  class="slide-icon swiper2-btn-left slide-icon-left"
             onclick="toPrev2()"
             onfocusin="toPrev2()"
             src="./../../images/middleImg/icon-slide.svg">
        <img role="button" alt="next"  id="swiper2RightArrow" tabindex="242" class="slide-icon swiper2-btn-right slide-icon-right"
             onclick="toNext2()"
             onfocusin="toNext2()"
             src="./../../images/middleImg/icon-slide.svg">

             <!-- Travel Recommendations轮播 PC和pad端数据-->
         <div class="swiper-wrapper">
             <% data.travelRecommendationsData.forEach(function(card,index) { %>
                 <div class="swiper-slide">
                     <div class="pc-scroll-wrap-inner row gap-4">
                        <div class="card col-12" tabindex="<%= 244 + index * 2 %>">
                             <img alt="<%= card.alt %>" class="card-img" src="<%= card.imgSrc %>">
         
                             <div class="card-content">
                                 <div class="place">
                                     <img alt="fligt icon" src="./../../images/middleImg/icon-fligt.svg">
                                     <span><%= card.place %></span>
                                 </div>
         
                                 <div class="desc">
                                     <%= card.description %>
                                 </div>
                             </div>
         
                             <div class="more" role="button" tabindex="<%= 245 + index * 2 %>">
                                 <span><%= card.moreText %></span>
                                 <img alt="<%= card.moreIconAlt %>" src="<%= card.moreIconSrc %>">
                             </div>
                         </div>
                     </div>
                 </div>
             <% }); %>
         </div>

        <div class="swiper2-pagination"></div>
    </div>


    <!-- Travel Recommendations轮播 手机端数据-->
<div class="pc-scroll-mobile-wrap row gap-2">
    <% data.travelRecommendationsMobileData.forEach(function(card,index) { %>
        <div class="card col-12">
            <img alt="<%= card.alt %>" class="card-img" src="<%= card.imgSrc %>">

            <div class="card-content">
                <div class="place">
                    <img alt="fligt icon" src="./../../images/middleImg/icon-fligt.svg">
                    <span><%= card.place %></span>
                </div>
            </div>
        </div>
    <% }); %>
</div>

</section>


<!-- Product recommendation 三端 -->
<section class="section">
    <div class="section-title">Product recommendation</div>

    <!-- Product recommendation 数据 -->
 <!-- Product recommendation 内容 -->
    <div class="grid-card row gap-4">
        <% data.productRecommendationData.forEach(function(card) { %>
            <div class="card col-sPC-6 col-12 col-PC-6">
                <img alt="<%= card.alt %>" class="card-img" src="<%= card.imgSrc %>">
                <div class="card-content">
                    <div class="card-title"><%= card.title %></div>
                    <div class="card-text">
                        <%= card.description %>
                    </div>

                    <div class="btn card-text" role="button">
                        <%= card.moreText %>
                        <img alt="learn more" src="<%= card.moreIconSrc %>">
                    </div>
                </div>
            </div>
        <% }); %>
    </div>
</section>


<!--Star Alliance 三端  -->
<section class="section">
    <div class="section-title">Star Alliance</div>

    <div class="content row gap-0">
        <img alt=""
             class="card-img col-PC-6 col-sPC-6 col-pad-12 col-mobile-12"
             src="./../../images/middleImg/star-alliance-img1.jpg">
        <div class="info-card col-PC-6 col-sPC-6 col-pad-12 col-mobile-12">
            <div class="info-card-title single-line">
                A Star Alliance ticket for an unlimited journey
            </div>

            <p class="text">
                Star League Global Package allows you to travel to as many as 15 destinations around the world on a
                trip.
                <br>
                Book your Star League Global Package by staralliance.com/roundtheworld.
                <br>
                The journey of your dream, just a few clicks, can become reality.
            </p>

            <div class="btn" role="button">Booking</div>
        </div>
    </div>
</section>


<!-- event recommendation 三端 -->
<section class="section">
    <div class="section-title">Event Recommendation</div>

    <div class="pc-recommendation-wrap swiper swiper6">

        <!-- 如果需要导航按钮 -->
        <img role="button" alt="" class="slide-icon slide-icon-left"
             onclick="toSwiper6(-1)"
             onfocusin="toSwiper6(-1)"
             id="swiper6LeftArrow"
             tabindex="340"
             src="./../../images/middleImg/icon-slide.svg">
        <img role="button" alt="" class="slide-icon slide-icon-right"
             onclick="toSwiper6(1)"
             onfocusin="toSwiper6(1)"
             id="swiper6RightArrow"
             tabindex="342"
             src="./../../images/middleImg/icon-slide.svg">

          <!-- event recommendation 数据 -->
        <div class="swiper-wrapper">
            <% data.eventRecommendationData.forEach(function(card,index) { %>
                <div class="swiper-slide recommendation-card" tabindex="<%= 344 + index * 2 %>">
                    <img alt="" class="recommendation-card-img" src="<%= card.imgSrc %>">

                    <div class="recommendation-card-info">
                        <div class="recommendation-card-info-title"><%= card.title %></div>

                        <div class="recommendation-card-info-desc">
                            <%= card.description %>
                        </div>
                    </div>

                    <div class="more" role="button" tabindex="<%= 345 + index * 2 %>">
                        <%= card.moreText %>
                        <img alt="" src="<%= card.moreIconSrc %>">
                    </div>
                </div>
            <% }); %>
         
        </div>
    </div>
</section>
</div>

