// banner------------------------------------- start

.slide-banner {
  position : relative;
  margin: 0 auto;
  max-width: 1920px;
  height   : 520px;
  z-index: 5;
  overflow : hidden;
  .slide-nav {
    position    : relative;
    float       : right;
    height      : 30px;
    line-height : 30px;
    z-index     : 1;

    & > li {
      display          : inline-block;
      width            : .6rem;
      height           : .6rem;
      border-radius    : 50%;
      background-color : #FFF;
      margin-left      : .3rem;
      cursor           : pointer;

      &:hover {
        background-color : @primary-color;
      }

      &.active {
        background-color : @primary-color;
      }
    }
  }
  .slide-content {
    position : relative;
    z-index  : 1;
    top      : 0;
    width    : 99999px;
    height   : 100%;

    .slide-item {
      position : relative;
      float    : left;
      height   : 100%;

      img {
        width  : auto;
        height : 100%;
        margin-left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .link-slide {
    display : block;
    width   : 100%;
    height  : 100%;
    overflow: hidden;
  }

  .paginator {
    position: absolute;
    right: 2rem;
  }
  .play-btn {
    float       : right;
    margin-left: .5rem;
    border      : none;
    position    : relative;
    height      : 30px;
    line-height : 30px;
    z-index     : 1;
    color       : #fff;
    cursor      : pointer;
    width       : 25px;
    text-align  : center;

    .icon-zh {
      font-size: 0.6rem;
      &:hover {
        color: @primary-color;
      }
    }
    .icon-zh-pause {
      display   : inline-block;
    }

    .icon-zh-play {
      display     : none;
    }

    &.on {
      .icon-zh-play  {
        display : inline-block;
      }

      .icon-zh-pause {
        display : none;
      }
    }
  }

  // 滚动新闻及轮播按钮
  .control-box {
    position: relative;
    bottom: 7.2rem;
    z-index: 99;
    .pagination-wrap {
      display         : flex;
      align-items     : center;
      justify-content : space-between;

      .banner-info {
        width           : calc(100% - 8rem);
        height          : 1.5rem;
        line-height     : 1.5rem;
        display         : flex;
        align-items     : center;
        justify-content : space-between;
        background      : rgba(235, 213, 156, 0.8);
        padding         : 0 0.95rem;
        box-sizing      : border-box;
        font-size       : 0.7rem;
        color           : #101010;

        .icon-horn {
          margin-right : 0.3rem;
        }
      }
    }
  }
}

.link-tab {
  opacity : 0;
  width   : 0;
  height  : 0;
}
// .pc-small {
//   display: none;
// }
// banner------------------------------------- end

// -------------------------------------
// form表单样式，与轮播图不冲突
@media screen and (min-width : 1025px) /* PC */ {
  .top-level-plane {
    position : relative;
    z-index  : 5;
    margin   : -5rem auto 0;
  }
  // banner图片变化
  .slide-banner {
    .banner-group {
      // .pad-bg,.phone-bg,.pc-bg {
      //   display: none;
      // }
      // .pc-small {
      //   display: block;
      //   width: 1920px;
      // }
    }
  }
}
@media screen and (min-width : 1025px) and (max-width : 1439px) {
  .top-level-plane {
    margin   : -3rem auto 0;
  }
  // banner图片变化
  .slide-banner {
    .control-box {
      bottom: 5rem;
    }
  }
}

/* ipad */
@media screen and (max-width : 1024px) {

  .top-level-plane {
    position : relative;
    z-index  : 5;
    margin   : -3rem auto 0;
  }

  .slide-banner {
    .control-box {
      bottom: 5rem;
      .pagination-wrap {
        .banner-info {
          width           : calc(100% - 8rem);
          height          : 1.5rem;
          line-height     : 1.5rem;
        }

      }
    }
    .banner-group{
      // .phone-bg ,.pc-bg, .pc-small {
      //   display: none;
      // }
      // .pad-bg {
      //   display: block;
      //   width: 768px;
      // }
    }
  }
}

/* mobile */
@media screen and (max-width : 767px) {

  .top-level-plane {
    position : relative;
    z-index  : 5;
    margin   : -2.6rem auto 0;
  }
  .slide-banner {
    .control-box {
      bottom: 4rem;
      .pagination-wrap {
        .banner-info {
          width           : calc(100% - 8rem);
          height          : 1rem;
          line-height     : 1rem;
        }
      }
      .paginator {
        right: .5rem;
        bottom: -0.2rem;
      }
    }
    .banner-group{
      // .pc-bg,.pad-bg {
      //   display: none;
      // }
      // .phone-bg{
      //   display: block;
      //   width: 375px;
      // }
    }
  }
}

@media screen and (min-width : @screen-pc) {
  // banner图片变化
  .slide-banner {
    .banner-group {
      // .pad-bg,.phone-bg,.pc-small {
      //   display: none;
      // }
      // .pc-bg {
      //   display: block;
      //   width: 1920px;
      // }
    }
  }
}
