/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: '<PERSON><PERSON>', 'Microsoft YaHei', sans-serif;
}

html {
  font-family: sans-serif; // 2
  line-height: 1.15; // 3
  font-size: 20px;
  -webkit-text-size-adjust: 100%; // 4
  -ms-text-size-adjust: 100%; // 4
  -ms-overflow-style: scrollbar; // 5
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); // 6
}

.header-section {
  background: url(../images/distcount/pc/header.jpg) no-repeat top center;
}

body {
  background: url(../images/distcount/pc/center.jpg) repeat center center;
  background-size: cover;
  color: #333;
  position: relative;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  // overflow: hidden;
  position: relative;
}

/* 顶部标志和背景 */
.header {
  position: relative;
  overflow: hidden;
}

.logo-container {
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.logo-box {
  display: flex;
  align-items: flex-start;

  .expo-logo {
    display: block;
    background: url(../images/distcount/expo_logo.png) no-repeat center center;
    background-size: cover;
    width: 112.66px;
    height: 170px;
  }

  .airline-logo {
    display: block;
    background: url(../images/distcount/airline_logo.png) no-repeat center center;
    background-size: cover;
    width: 150px;
    height: 170px;
  }


}

.hero-content {
  font-family: Source Han Sans;

  position: relative;
  margin-left: 88px;
  margin-top: 164px;
  z-index: 2;

  .hero-text {
    margin-bottom: 20px;
  }

  .hero-text h1 {
    font-size: 70.66px;
    color: #333;
    margin-bottom: 5px;
  }

  .hero-text .highlight {
    color: #e62129;
    font-weight: bold;
  }

  .subtitle {
    font-size: 20px;
    font-weight: 300;
    color: #666666;
  }

  .expo-date {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    display: flex;

    .date-label {
      background-color: #333;
      color: white;
      font-size: 32px;
      padding: 0 6px;
      margin-right: 10px;
      line-height: 1.4;
    }

    .date-value {

      font-family: Allan;
      font-size: 40px;
      color: #3D3D3D;
      font-style: italic;
      margin-left: 26px;
    }

  }
}


/* 旅行选项卡 */
.trip-tabs {
  display: flex;
  margin: 106px auto 43px;
  // max-width: 500px;
  overflow: hidden;
  padding: 0 20px;
  gap: 30px;

  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  .tab-btn {
    flex: 1;
    padding: 15px;
    border: none;
    background-color: #e0e0e0;
    cursor: pointer;
    font-size: 16px;
    border-radius: 30px;

    transition: all 0.3s;
  }

  .tab-btn.active {
    background-color: #1a5cb0;
    color: white;
  }
}



/* 航班列表 */
.flight-list {
  padding: 0 20px;
  // max-width: 900px;
  margin: 0 auto;

  .flight-item {
    // opacity: 0.7;
    // background: #F3F3F3;
    // backdrop-filter: blur(10px);
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);


    opacity: 0.9;
    background: #F3F3F3;
    backdrop-filter: blur(10px);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    padding: 10px 32px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    position: relative;

    &::before {
      content: '';
      display: block;
      position: absolute;
      left: -2px;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 0 50% 50% 0;
      width: 14px;
      height: 20px;
      background: #d8d8d8;
    }

    &::after {
      content: '';
      display: block;
      position: absolute;
      right: -2px;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 50% 0 0 50%;

      width: 14px;
      height: 20px;
      background: #d8d8d8;
      // overflow: hidden;
    }
  }

  .discount-badge {
    position: absolute;
    left: -28px;
    top: 50%;
    transform: translateY(-50%);
    width: 76px;
    height: 76px;
    background: url(../images/distcount/discount_badge.png) no-repeat center center;
    background-size: cover;
    z-index: 1;
  }


  .flight-route {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 322px;
    padding: 0 32px;
    flex: 1;
    min-width: 200px;
  }

  .city-from,
  .city-to {
    display: flex;
    align-items: center;
    min-width: 140px;
    justify-content: center;

  }
  .city-to {
    justify-content: center;
  }

  .city-name {
    font-size: 24px;
    font-weight: bold;
    color: #CC0100;
    text-wrap: nowrap;
    padding: 8px 0;
  }

  .city-name-en {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .route-icon {
    // margin: 0 15px;
    height: 20px;
    width: 20px;
    background: url(../images/distcount/svg/sideway_single.svg) no-repeat center center;
    &.return {
      background: url(../images/distcount/svg/sideway_return.svg) no-repeat center center;
    }
  }

  .flight-divider {
    width: 1px;
    height: 100px;
    background-color: #D8D8D8;
    // margin: 1px;
  }

  .flight-date {
    display: flex;
    flex: 1;
    padding: 0 32px;
    align-items: center;

    .date-label {
      font-family: PingFang SC;
      font-size: 20px;
      color: #666666;
      text-wrap: nowrap;
    }

    .date-value {
      margin-left: 40px;
      font-size: 20px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: normal;
      color: #05164D;
    }

    .calendar-group {
      display: flex;
      // flex-wrap: nowrap;
      justify-content: flex-start;

      .calendar-split {
        padding: 0 5px;
      }
      &.single-group {
        .calendar-split {
          display: none;
        }
        .return-input {
          display: none;
        }
      }
    }

    .calendar-group .calendar-input {
      font-family: PingFang SC;
      padding: 0px;
      width: 120px;
      font-weight: 600;
      line-height: normal;
      letter-spacing: normal;
      font-size: 20px;
      color: #05164D;
      border: none;
      background-color: transparent;

      &.depart-input {
        text-align: right;
      }


    }
  }

  .book-btn {
    border-radius: 20px;
    background: #CC0100;
    text-align: center;
    width: 200px;
    color: white;
    border: none;
    font-family: Source Han Sans;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    letter-spacing: 0.1em;
    color: #FFFFFF;
    cursor: pointer;
    img {
      vertical-align: -2px;
    }
  }
}


/* 底部波浪 */
.bottom-waves {
  height: 203px;
  background: url(../images/distcount/pc/footer.jpg) no-repeat center center;
  background-size: cover;
}

@media screen and (max-width: 767px) {

  .logo-box {
    .expo-logo {
      width: 31px;
      height: 47px;
    }

    .airline-logo {
      width: 41px;
      height: 47px;
    }
  }

  .hero-content {
    margin-top: 35vw;
    margin-left: 20px;

    .hero-text {
      margin-bottom: 20px;
    }

    .hero-text h1 {
      font-size: 28px;
    }

    .subtitle {
      font-size: 12px;
    }

    .expo-date {

      .date-label {
        font-size: 12px;
      }

      .date-value {
        margin-left: 10px;
        font-size: 12px;

      }

    }
  }

  .trip-tabs {
    margin-top: 41px;
    gap: 7px;
  }
}

@media screen and (max-width: 900px) {
  .flight-list {
    .discount-badge {
      width: 44px;
      height: 44px;
      top: 10px;
      left: -14px;
      transform: none;
    }

  }

  /* 航班列表 */
  .flight-list {
    .flight-item {
      flex-direction: column;
      padding: 10px 30px;

    }

    .city-name {
      font-size: 20px;
    }

    .city-name-en {
      font-size: 12px;
    }

    .flight-route {
      padding: 0 10px;
      width: 100%;
    }

    // .route-icon {
    //   margin: 0 15px;
    //   color: #666;
    // }

    .flight-divider {
      height: 1px;
      width: 100%;
    }

    .flight-date {
      margin-left: 0;
      padding: 16px 10px;
      justify-content: flex-start;
      width: 100%;

      .date-label {
        font-size: 12px;
      }

      .date-value {
        font-size: 14px;
        margin-left: 28px;
      }

      .calendar-group {
        display: flex;
        // flex-wrap: nowrap;
        justify-content: flex-start;

        .calendar-split {
          padding: 0 5px;
        }
      }

      .calendar-group .calendar-input {
        width: 86px;
        font-size: 14px;

        &.depart-input {
          text-align: right;
        }
      }
    }

    .book-btn {
      width: 100%;
      margin-bottom: 7px;

    }

    .header::before {
      opacity: 1;
      width: 60%;
    }

    .logo-box {
      max-width: 400px;
    }

    .expo-logo {
      padding: 15px;
    }

    .red-circle {
      width: 60px;
      height: 60px;
    }

    .red-circle::before {
      width: 45px;
      height: 45px;
    }

    .expo-logo span {
      font-size: 18px;
    }

    .airline-icon {
      width: 40px;
      height: 30px;
    }

    .airline-text {
      font-size: 14px;
    }

    .hero-text h1 {
      font-size: 36px;
    }

    .subtitle {
      font-size: 16px;
    }

  }
}


@media screen and (min-width : 768px) and (max-width : 1024px) {

  .hero-content {
    margin-top: 240px;
    margin-left: 40px;

    .hero-text {
      margin-bottom: 20px;
    }

    .hero-text h1 {
      font-size: 28px;
    }

    .subtitle {
      font-size: 12px;
    }

    .expo-date {

      .date-label {
        font-size: 12px;
      }

      .date-value {
        margin-left: 10px;
        font-size: 12px;

      }

    }
  }

  .trip-tabs {
    margin-top: 41px;
    gap: 7px;
  }

}

@media screen and (min-width : 901px) and (max-width : 1024px) {

  /* 航班列表 */
  .flight-list {

    .discount-badge {
      width: 60px;
      height: 60px;
      left: -20px;
    }

    
    .flight-item {
      // flex-direction: column;
      padding: 10px 20px;
    }

    .city-name {
      font-size: 20px;
    }

    .flight-route {
      padding: 0 12px;
    }

    .flight-date {
      margin-left: 0;
      padding: 16px 10px;
      justify-content: flex-start;
      width: 100%;

      .date-label {
        font-size: 16px;
      }

      .date-value {
        font-size: 16px;
        // margin-left: 28px;

      }

      .calendar-group {
        flex-direction: column;
        margin-left: 20px;
        min-width: 100px;

        .calendar-split {
          padding: 0 5px;
        }
      }

      .calendar-group .calendar-input {
        width: 85px;
        font-size: 14px;

        &.depart-input {
          text-align: left;
        }

      }
    }

  }
}

@media screen and (min-width: 1025px) and (max-width: 1440px) {

  .flight-list {

    .flight-date {

      .calendar-group {
        display: flex;
        // flex-wrap: nowrap;
        flex-direction: column;
        justify-content: flex-start;
        min-width: 100px;
        margin-left: 20px;

        .calendar-split {
          padding: 0 5px;
        }
      }

      .calendar-group .calendar-input {
        width: 120px;

        &.depart-input {
          text-align: left;
        }

      }
    }


  }
}

@media (min-width: 1025px) {
  .container {
    max-width: 1400px;
  }
}


@media screen and (max-width: 430px) {
  .flight-list {
    .flight-item {
      padding: 10px 20px;
    }
    .city-from, .city-to {
      min-width: 120px;
    }
    .flight-route {
      padding: 0;
      width: 100%;
    }
    .city-name {
      font-size: 18px;
      // padding: 8px 0;
   }
    .flight-date {
      padding: 16px 0;

      .date-value {
        font-size: 12px;
        margin-left: 0px;
      }
      .calendar-group .calendar-input {
        font-size: 12px;
      }
    }
  }
}


// <---- 背景适配 -- >
@media screen and (max-width: 440px) {
  .flight-list {
    .flight-date {
      // padding: 16px 0;

      .date-value {
        // font-size: 12px;
        margin-left: 0;
      }

    }
  }
  .header-section {
    background-image: url(../images/distcount/mobile/header.jpg);
  }

  .bottom-waves {
    height: 100px;
    background-image: url(../images/distcount/mobile/footer.jpg);
  }

  body {
    background-image: url(../images/distcount/mobile/center.jpg);
  }
}

@media (min-width : 441px) and (max-width: 767px) {
  .header-section {
    background-image: url(../images/distcount/mobile_p/header.jpg);
    background-size: contain;
  }

  .bottom-waves {
    height: 100px;
    background-image: url(../images/distcount/mobile_p/footer.jpg);
  }

  body {
    background-image: url(../images/distcount/mobile_p/center.jpg);
    background-position-y: top;
  }
}

@media (min-width : 768px) and (max-width: 900px) {
  .logo-box {
    .expo-logo {
      width: 84px;
      height: 127px;
    }

    .airline-logo {
      width: 112px;
      height: 127px;
    }
  }

  .header-section {
    background-image: url(../images/distcount/pad/header.jpg);
  }

  .bottom-waves {
    height: 188px;
    background-image: url(../images/distcount/pad/footer.jpg);
  }

  body {
    background-image: url(../images/distcount/pad/center.jpg);
  }
}

@media (min-width : 901px) and (max-width: 1024px) {
  .logo-box {
    .expo-logo {
      width: 84px;
      height: 127px;
    }

    .airline-logo {
      width: 112px;
      height: 127px;
    }
  }

  .header-section {
    background-image: url(../images/distcount/pad_p/header.jpg);
  }

  .bottom-waves {
    height: 188px;
    background-image: url(../images/distcount/pad_p/footer.jpg);
  }

  body {
    background-image: url(../images/distcount/pad_p/center.jpg);
  }
}