<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>世博会旅行预订 | World Expo Osaka 2025</title>
  <link href="../../css/sibo.css" rel="stylesheet" />
</head>

<body>
  <div class="page">
    <div class="header-section">
      <div class="container">
        <!-- 顶部标志和背景 -->
        <header class="header">
          <div class="logo-container">
            <div class="logo-box">
              <div class="expo-logo">
              </div>
              <div class="airline-logo">
              </div>
            </div>
          </div>

          <div class="hero-content">
            <div class="hero-text">
              <h1>邀您一同体验<br><span class="highlight">世博会</span>的魅力</h1>
              <p class="subtitle">Welcome to World Expo Osaka 2025</p>
            </div>

            <div class="expo-date">
              <div class="date-label">销售/旅行日期 / Date</div>
              <div class="date-value">2025/05/12-2025/10/13</div>
            </div>
          </div>
        </header>
      </div>
    </div>
    <div class="center-section">

      <div class="container">

        <!-- 旅行选项卡 -->
        <div class="trip-tabs">
          <button class="tab-btn active" id="one-way-btn">单程 / One way</button>
          <button class="tab-btn" id="round-trip-btn">往返 / Round trip</button>
        </div>

        <!-- 航班列表 -->
        <div class="flight-list">
          <!-- 札幌-深圳 -->
          <div class="flight-item">

            <div class="discount-badge"></div>

            <div class="flight-route">
              <div class="city-from">
                <span class="city-name">札幌</span>
                <span class="city-name-en">(Sapporo)</span>
              </div>

              <div class="route-icon"></div>

              <div class="city-to">
                <span class="city-name">深圳</span>
                <span class="city-name-en">(Shenzhen)</span>
              </div>
            </div>
            <div class="flight-divider"></div>
            <div class="flight-date">
              <div class="date-label"><span class="route-type-date-label">出发日期</span> / Date</div>
              <div class="date-value calendar-group single-group">
                <div><input type="text" class="calendar-input depart-input group-input bussiness-depart-input" readonly /><span
                    class="calendar-split">-</span></div>
                <input type="text" class="calendar-input return-input group-input bussiness-return-input disable" readonly />
              </div>
            </div>
            <div class="book-btn">
              立即预订 / BOOK <img src="./../../images/distcount/svg/flight.svg">
            </div>
          </div>

          <!-- 名古屋-深圳 -->
          <div class="flight-item">
            <div class="discount-badge"></div>

            <div class="flight-route">
              <div class="city-from">
                <span class="city-name">名古屋</span>
                <span class="city-name-en">(Nagoya)</span>
              </div>

              <div class="route-icon"></div>


              <div class="city-to">
                <span class="city-name">深圳</span>
                <span class="city-name-en">(Shenzhen)</span>
              </div>
            </div>
            <div class="flight-divider"></div>

            <div class="flight-date">
              <div class="date-label"><span class="route-type-date-label">出发日期</span> / Date</div>
              <div class="date-value calendar-group single-group">
                <div><input type="text" class="calendar-input depart-input group-input bussiness-depart-input" ><span
                    class="calendar-split">-</span></div>

                <input type="text" class="calendar-input return-input group-input bussiness-return-input disable" readonly />
              </div>
            </div>
            <div class="book-btn">
              立即预订 / BOOK <img src="./../../images/distcount/svg/flight.svg">
            </div>
          </div>

          <!-- 大阪-深圳 -->
          <div class="flight-item">
            <div class="discount-badge"></div>

            <div class="flight-route">
              <div class="city-from">
                <span class="city-name">大阪</span>
                <span class="city-name-en">(Osaka)</span>
              </div>

              <div class="route-icon"></div>


              <div class="city-to">
                <span class="city-name">深圳</span>
                <span class="city-name-en">(Shenzhen)</span>
              </div>
            </div>
            <div class="flight-divider"></div>

            <div class="flight-date">
              <div class="date-label"><span class="route-type-date-label">出发日期</span> / Date</div>
              <div class="date-value calendar-group single-group">
                <div><input type="text" class="calendar-input depart-input group-input bussiness-depart-input" readonly /><span
                    class="calendar-split">-</span></div>

                <input type="text" class="calendar-input return-input group-input bussiness-return-input disable" readonly />
              </div>
            </div>
            <div class="book-btn">
              立即预订 / BOOK <img src="./../../images/distcount/svg/flight.svg">
            </div>
          </div>

          <!-- 大阪-无锡 -->
          <div class="flight-item">
            <div class="discount-badge"></div>

            <div class="flight-route">
              <div class="city-from">
                <span class="city-name">大阪</span>
                <span class="city-name-en">(Osaka)</span>
              </div>

              <div class="route-icon"></div>


              <div class="city-to">
                <span class="city-name">无锡</span>
                <span class="city-name-en">(Wuxi)</span>
              </div>
            </div>
            <div class="flight-divider"></div>

            <div class="flight-date">
              <div class="date-label"><span class="route-type-date-label">出发日期</span> / Date</div>
              <div class="date-value calendar-group single-group">
                <div><input type="text" class="calendar-input depart-input group-input bussiness-depart-input" readonly /><span
                    class="calendar-split">-</span></div>

                <input type="text" class="calendar-input return-input group-input bussiness-return-input disable" readonly />
              </div>
            </div>
            <div class="book-btn">
              立即预订 / BOOK <img src="./../../images/distcount/svg/flight.svg">
            </div>
          </div>

          <!-- 大阪-南京 -->
          <div class="flight-item">
            <div class="discount-badge"></div>

            <div class="flight-route">
              <div class="city-from">
                <span class="city-name">大阪</span>
                <span class="city-name-en">(Osaka)</span>
              </div>

              <div class="route-icon"></div>


              <div class="city-to">
                <span class="city-name">南京</span>
                <span class="city-name-en">(Nanjing)</span>
              </div>
            </div>
            <div class="flight-divider"></div>

            <div class="flight-date">
              <div class="date-label"><span class="route-type-date-label">出发日期</span> / Date</div>
              <div class="date-value calendar-group single-group">
                <div><input type="text" class="calendar-input depart-input group-input bussiness-depart-input" readonly /><span
                    class="calendar-split">-</span></div>

                <input type="text" class="calendar-input return-input group-input bussiness-return-input disable" readonly />
              </div>
            </div>
            <div class="book-btn">
              立即预订 / BOOK <img src="./../../images/distcount/svg/flight.svg">
            </div>
          </div>

          <!-- 大阪-南通 -->
          <div class="flight-item">
            <div class="discount-badge"></div>

            <div class="flight-route">
              <div class="city-from">
                <span class="city-name">大阪</span>
                <span class="city-name-en">(Osaka)</span>
              </div>

              <div class="route-icon"></div>


              <div class="city-to">
                <span class="city-name">南通</span>
                <span class="city-name-en">(Nantong)</span>
              </div>
            </div>
            <div class="flight-divider"></div>

            <div class="flight-date">
              <div class="date-label"><span class="route-type-date-label">出发日期</span> / Date</div>
              <div class="date-value calendar-group single-group">
                <div><input type="text" class="calendar-input depart-input group-input bussiness-depart-input" readonly /><span
                    class="calendar-split">-</span></div>

                <input type="text" class="calendar-input return-input group-input bussiness-return-input disable" readonly />
              </div>
            </div>
            <div class="book-btn">
              立即预订 / BOOK <img src="./../../images/distcount/svg/flight.svg">
            </div>
          </div>
        </div>
      </div>
      <!-- 底部波浪 -->
      <div class="bottom-waves"></div>
    </div>
  </div>
  <script>window.jQuery || document.write('<script src="../../plugins/jquery-3.7.1.min.js"><\/script>')</script>

  <script src="../../plugins/datepicker/datepicker_new.js"></script>
  <script>
    $(document).ready(function () {
      // 初始化日期选择器
      let defaultDate = new Date('2025-05-12');
      let defaultDateStr = '2025-05-12';
      let limitStartStr = '2025-05-12';
      if (new Date() > defaultDate) {
        defaultDate = new Date()
        let yy = defaultDate.getFullYear();
        let mm = defaultDate.getMonth() + 1 < 10 ? "0" + (defaultDate.getMonth() + 1) : defaultDate.getMonth() + 1;
        let dd = defaultDate.getDate() < 10 ? "0" + defaultDate.getDate() : defaultDate.getDate();
        limitStartStr = defaultDateStr = yy + "-" + mm + "-" + dd;
      };
     // 日期控件初始化
      $('.calendar-input').dateRangePicker({
        'classname': '.group-input',
        'defaultDate': defaultDateStr,
        'limitStart': limitStartStr,
        'limitEnd': '2025-10-13',
      });
      $('.depart-input').on('change', function () {
        $(this).closest('.calendar-group').find('.return-input').trigger('focus');
      })
        // 选项卡切换功能
      $('#one-way-btn').click(function() {
          $(this).addClass('active');
          $('#round-trip-btn').removeClass('active');
          $('.route-type-date-label').text('出发日期')
          $('.calendar-group').addClass('single-group').find('.return-input').addClass('disable');
          $('.route-icon').removeClass('return');
      });
      
      $('#round-trip-btn').click(function() {
          $(this).addClass('active');
          $('#one-way-btn').removeClass('active');
          $('.route-type-date-label').text('旅行日期')
          $('.calendar-group').removeClass('single-group').find('.return-input').removeClass('disable');
          $('.route-icon').addClass('return');
          $('.calendar-group').each((index,el) =>{
            let startDate = $(el).find('.depart-input').attr('data-date');
            let endDate = $(el).find('.return-input').attr('data-date');
            if(new Date(startDate) > new Date(endDate)){
              // 如果出发日期大于返回日期，则将返回日期设置为出发日期加2天,如果nextDate大于10月13号，则设置为10月13号,时间格式为yyyy-mm-dd
              let nextDate = new Date(startDate);
              nextDate.setDate(nextDate.getDate() + 2);
              
              endDate = nextDate.getFullYear() + '-' + 
                       String(nextDate.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(nextDate.getDate()).padStart(2, '0');
              if(new Date(endDate) > new Date('2025-10-13')){
                endDate = '2025-10-13';
              }
              $(el).find('.return-input').val(endDate);
              $(el).find('.return-input').attr('data-date',endDate);
            }
          })

      });
  
    });
  </script>
</body>

</html>