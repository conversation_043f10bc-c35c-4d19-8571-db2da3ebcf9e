@import "../../less/variables";
@minHeight: 22.8rem;
@scrollHeight: @minHeight - 2.1rem;

.city-origin {
   position: relative;
   &.input-single .icon-zh-right-type1 {
      position: initial;
      transform: rotate(0);
   } 
}

.city-panel {
   position: absolute;
   visibility: hidden;
   width: 100%;
   opacity: 0;
   top: 0;
   background-color: transparent;
   z-index: 3;
   transition: all .5s ease;

   .city-render {
      position: relative;
      min-height: @minHeight;
      overflow: hidden;
   }

   .continent-title {
      font-size: 0.7rem;
      padding: 0.5rem;
      height: 2rem;
      border-bottom: 1px solid @gray-lighter;
      color: #4F3D1E;
   }

   .city-text {
      color: @gray-101010;
   }

   .city-list-wrap {
      max-height: @scrollHeight;
      overflow: hidden;
      overflow-y: auto;
   }

   .city-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      border-bottom: 1px solid #ccc;
      font-size: 0.8rem;
      padding: 1rem 0.5rem;

      &.active {
         background-color: @picker-color-010;
         color: @color-white;
         .city-text {
            color: @color-white;
         }
      }

      &:last-child {
         border-bottom: none;
      }

      &:hover {
         background-color: @picker-color-010;
         color: @color-white;
         .city-text {
            color: @color-white;
         }
      }

      &:focus {
         outline: none;
         border: none;
      }
   }

   .city-continent,
   .city-airport,
   .city-search {
      z-index: 9;
      position: absolute;
      width: 100%;
      border: 1px solid @gray-lighter;
      background-color: @color-white;
   }

   .city-airport {
      left: 100%;
      visibility: hidden;
      transition: all .5s ease;

      &.show-airport {
         left: 0;
         visibility: visible;
         background: #fff;
         z-index: 10;
         transition: all .5s ease;

         .city-list:last-child {
            border-bottom: 1px solid #ccc;
         }
      }
   }

   .city-search {
      display: none;

      &.show-search {
         display: block;
         background-color: @color-white;

         .city-list-wrap {
            max-height: @minHeight - 0.1rem;
         }
      }
   }

   .no-result {
      height: 14rem;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.5rem;
      text-align: center;
   }

   .back-continent {
      font-size: 0.7rem;
      padding: 0.5rem;
      height: 2rem;
      color: @picker-color-010;
      border-bottom: 1px solid @gray-lighter;
   }

   .back-to {
      cursor: pointer;
      vertical-align: middle;

      .icon-zh {
         margin-right: .5rem;
      }
   }
}

.show-panel {
   .city-panel {
      width: 100%;
      top: 100%;
      opacity: 1;
      visibility: visible;
      transition: all .5s ease;
   }
}