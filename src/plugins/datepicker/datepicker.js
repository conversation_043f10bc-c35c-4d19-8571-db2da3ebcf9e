/* ========================================================================
 * HX: datepicker  v1.0.0  日历组件
 * Author: zjjing
 * ========================================================================
 * Copyright 2024-~ Travelsky ICED
 * response datepicker compoment
 * ======================================================================== */
+function ($) {
    'use strict';
    // 全局对象
      // 记录点击时的input对象
      var input_this;
    // 判断当前点击的是去程还是返程 0为去程输入框 1为返程输入框 默认点击的是去程输入框
      var isClick_roundTrip = 0;
      // 禁用今天以前的日期
      var datepickerDisabledBefore = true;
      // 禁用这个日期之前的日期 data-disable-last-date
      var disableLastDate;
      // 滚动 提前50px触发加载
      const triggerThreshold = 30;

      // 防抖函数（简易版）
      function debounce(func, delay = 200) {
        let timeout;
        return function() {
          const context = this, args = arguments;
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(context, args), delay);
        };
      }
    $.fn.dateRangePicker = function (options) {
        //获取当前年月日
        var year = new Date().getFullYear();
        var month = new Date().getMonth();
        var day = new Date().getDate();

        // 初始 月份，星期
        var month_rr = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        var week_rr = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

        // 开始时间  结束时间
        var start_date, end_date;

        // 默认开始时间和结束时间
        var default_start, default_end;

        // 滚动开始的年份
        var scroll_year = year;
        // 滚动开始的月份
        var scroll_month = month + 2;

        // 向下滚动的月份
        var scroll_month_min = month;
        // 向下滚动的年份
        var scroll_year_min = year;

        var $dateRangePicker = this;

        // input_this，group_length，isClick_roundTrip，has_disabled 在触发日历框的时候进行赋值


        // 当前点击的是单日历还是双日历框 1为单日历 2为双日历 默认单日历
        var group_length = 1;



        // 返程的日历框是否禁用
        var has_disabled = false; // 默认返程输入框没有被禁用

        // 日历宽度
        var datePickerWidth = 676;

        var defaults = {
            reference: $('body'),
            // month_rr:[],// 可以进行传参配置month的语言 必须12个 排序需要与默认值一致
            // week_rr:[],// 可以进行传参配置week的语言 必须7个 排序需要与默认值一致
            disabledBefore: true, //是否今天之前的都为灰色
            hasDefault: true, // 是否含有默认值
            bindFocus: true,// 需要键盘事件绑定 与hasDefault 同时使用
            nextDate: 2,// 回程日期是当前日期的2天以后
            languageType: 'default',  //默认语言类型
            disableClassName: 'disable', // 传入系统中禁用日历输入框的class名，没有就默认disable
            className: '.depart-input', // html结构中单程的className和往返程的往程className必须一致
        };
        var setting = $.extend({}, defaults, options);

        // if (setting.languageType !== 'default') {
        //     month_rr = $.dateRangePickerLanguages[setting.languageType].monthName;
        //     week_rr = $.dateRangePickerLanguages[setting.languageType].shortWeekName;
        // }
        // var $dateRangePicker = setting._this;
        var dateRangePicker = {
            setPos: function () {

            },

            init: function () {
                $.each($(setting.className).closest(".calendar-group"), function (key, obj) {
                    if (setting.hasDefault) { //  有默认值 往返程
                        var temp_month = month + 1 < 10 ? "0" + (month + 1) : month + 1;
                        var start_day = day < 10 ? "0" + day : day;
                        default_start = year + "-" + temp_month + "-" + start_day;
                        default_end = changeDate(setting.nextDate, default_start);
                        start_date = default_start;
                        end_date = default_end;
                        // $(setting.className).val(dateFammer(start_date,setting.languageType));
                        $(setting.className).val(start_date);
                        $(setting.className).attr("data-date", start_date);
                        $($(obj).find(".calendar-input")[1]).val(end_date);
                        $($(obj).find(".calendar-input")[1]).attr("data-date", end_date);
                    }

                    if (!setting.hasDefault && $(setting.className).closest(".calendar-group").find(".calendar-input").length === 1) { // 单程  默认值
                        var temp_month = month + 1 < 10 ? "0" + (month + 1) : month + 1;
                        var start_day = day < 10 ? "0" + day : day;
                        default_start = year + "-" + temp_month + "-" + start_day;
                        start_date = default_start;
                        // $dateRangePicker.val(dateFammer(start_date,setting.languageType))
                        $dateRangePicker.val(start_date);
                        $dateRangePicker.attr("data-date", start_date);
                    }
                });

                //定义一个html字符串
                var dayStr = createDom(year, month);
                // 添加到body下面
                if($(".date-range-picker").length === 0){
                  $('body').append(dayStr);

                }

                this.bind();
                this.show();
                this.close();
            },

            show: function () {

                $dateRangePicker.on("mousedown", function () {//阻断 focus事件
                    return false;
                });

                $dateRangePicker.on("click", function (event) {
                    $(window).bind("orientationchange", function () {
                        $(".date-range-picker").hide();
                        $(input_this).blur();
                        if (window.orientation == 90 || window.orientation == -90) {//ipad、iphone竖屏；Andriod横屏
                            scroll_month = month + 2;
                            $(".picker-con.right-con").not(":first").remove();
                            // alert('横屏状态！');
                        }
                        // if (window.orientation === 180 || window.orientation === 0) {
                        //     // alert('竖屏状态！');
                        // }
                    });

                    // 全局的赋值
                    input_this = $(this);// 赋值 this对象
                    // 当前点击的是单日历还是双日历框 1为单日历 2为双日历
                    group_length = input_this.closest(".calendar-group").find(".calendar-input").length;
                    // 判断当前点击的是去程还是返程 0为去程输入框 1为返程输入框
                    isClick_roundTrip = input_this.closest(".calendar-group").find(".calendar-input").index(input_this);
                    // 返程的日历框是否禁用
                    if (group_length === 2) {
                        has_disabled = $(input_this.closest(".calendar-group").find(".calendar-input")[1]).hasClass(setting.disableClassName);
                    }

                    // 展示面板处理方法
                    showDatePicker(event, this);
                    // 调用公共方法 this为input对象
                    focusAndClick(this);

                    $(this).unbind('focus');//解绑 focus事件
                    $(this).focus();// 聚焦
                });

                // 绑定focus事件
                bindFocus($dateRangePicker);

            },

            close: function () {

                //点击其他元素 隐藏date-range-picker
                $(document).on("click", function () {
                    $(".date-range-picker").hide();
                    bindFocus($dateRangePicker);//关闭的时候 重新绑定事件
                    $("body").css({
                        overflow: "",
                    });
                });

                //点击 X 隐藏元素
                $(".close-picker").on("click", function () {
                    $(".date-range-picker").hide();
                    input_this.unbind('focus');//解绑 focus事件
                    input_this.focus();// 聚焦
                    $("body").css({
                        overflow: "",
                    });
                });

                // 点击日历面板上面所有的元素都进行冒泡的阻止
                $(document).on("click", ".date-range-picker", function (event) {
                    event.stopPropagation();
                });
            },

            bind: function () {
                // 移动端滚动事件
                $(".date-range-picker .picker-body").scroll(debounce(function () {
                    var scroll_hight = $(this)[0].scrollHeight;
                    var scroll_top = $(this)[0].scrollTop;
                    var div_hight = $(this).height();
                    if (scroll_top + div_hight >= scroll_hight) { //40 上下间距,6个月内&& scroll_month - month < 6
                        if (scroll_month >= 12) { // 进行翻年判断
                            scroll_month = 0;
                          scroll_year = scroll_year + 1;
                        }

                        // 设置移动端背景图
                        var bgStyle = setBgForPhone(scroll_month);

                        var str_html = '';//= getMonth(year, scroll_month)
                        str_html += '<div class="picker-con right-con mobile-month">' +
                            '<div class="picker-hd"><h3 class="month-t">' + getHead(scroll_year, scroll_month) + '</h3></div>' +
                            '<table class="date-table" role="grid" cellspacing="0" border="0" cellpadding="0">' +
                            '<thead><tr class="date-row"><th>' + week_rr[0] + '</th><th>' + week_rr[1] + '</th><th>' + week_rr[2] + '</th><th>' + week_rr[3] + '</th><th>' + week_rr[4] + '</th><th class="red-color">' + week_rr[5] + '</th><th class="red-color">' + week_rr[6] + '</th></tr></thead>' +
                            `<tbody class="month-con" style="${bgStyle}">` +
                            getMonth(scroll_year, scroll_month) +
                            '</tbody></table>' +
                            '</div>';
                        if (scroll_month < 12) {
                            scroll_month++;
                        }
                        $(".picker-body").append(str_html);
                    }

                    // 往上滚动
                    if (scroll_top<=triggerThreshold) {
                      // 记录当前第一个月份元素的高度
                      const firstItemHeight = $(this).children().first().outerHeight(true);

                      if (scroll_month_min >0) {
                        scroll_month_min--;
                      } else {
                        scroll_year_min = scroll_year_min - 1;
                        scroll_month_min = 11
                      }

                      // 设置移动端背景图
                      var bgStyle = setBgForPhone(scroll_month_min);
                      var str_html = '';//= getMonth(year, scroll_month)
                      str_html += '<div class="picker-con right-con mobile-month">' +
                        '<div class="picker-hd"><h3 class="month-t">' + getHead(scroll_year_min, scroll_month_min) + '</h3></div>' +
                        '<table class="date-table" role="grid" cellspacing="0" border="0" cellpadding="0">' +
                        '<thead><tr class="date-row"><th>' + week_rr[0] + '</th><th>' + week_rr[1] + '</th><th>' + week_rr[2] + '</th><th>' + week_rr[3] + '</th><th>' + week_rr[4] + '</th><th class="red-color">' + week_rr[5] + '</th><th class="red-color">' + week_rr[6] + '</th></tr></thead>' +
                        `<tbody class="month-con" style="${bgStyle}">` +
                        getMonth(scroll_year_min, scroll_month_min) +
                        '</tbody></table>' +
                        '</div>';

                      $(".picker-body").prepend(str_html);
                      $(this).scrollTop(scroll_top+firstItemHeight)
                    }
                }));

                // 绑定键盘事件
                $dateRangePicker.keydown(function (event) {

                    //回车按键
                    if (event.keyCode === 13) {
                        $(".date-td.hover").focus();
                        event.stopPropagation();
                    }

                    if (event.keyCode === 9) {
                        $(".date-range-picker").hide();
                    }

                });

                $(".close-picker").keydown(function (event) {
                    if (event.keyCode === 13) {
                        input_this.focus();//焦点重回input
                        $(".date-range-picker").hide();
                    }
                    blockingMouse(event);
                    event.stopPropagation();
                });

                $(".preMonth").keydown(function (event) {

                    //回车
                    if (event.keyCode === 13) {
                        if (month > 11) {
                            year = year + 1;
                            month = 0;
                        }

                        if (month < 0) {
                            year = year - 1;
                            month = 11;
                        }

                        month = month - 1;

                        replace(year, month);

                        for (var i = 0; i < 7; i++) {
                            if (!$(".date-range-picker .left-con .date-row .date-td").eq(i).hasClass("empty-date") &&
                                !$(".date-range-picker .left-con .date-row .date-td").eq(i).hasClass("gray-date")) {
                                $(".date-range-picker .left-con .date-row .date-td").eq(i).addClass("hover").attr("tabindex", 0).focus();
                                break;
                            }
                        }
                        if ($(".date-range-picker .left-con .date-row .date-td").eq(0).hasClass("empty-date")) {
                            $(".date-td.start-date.active").addClass("hover").attr("tabindex", 0).focus();
                        }
                    }
                    blockingMouse(event);
                    event.stopPropagation();

                });

                $(".nextMonth").keydown(function (event) {

                    if (event.keyCode === 9) {
                        $(".date-range-picker").focus();
                    }

                    //回车
                    if (event.keyCode === 13) {
                        month = month + 1;
                        if (month > 11) {
                            year = year + 1;
                            month = 0;
                        }
                        if (month < 0) {
                            year = year - 1;
                            month = 11;
                        }

                        replace(year, month);

                        for (var i = 0; i < 7; i++) { // 没有active 状态下的 需要判断hover 第几个
                            if (!$(".date-range-picker .left-con .date-row .date-td").eq(i).hasClass("empty-date") &&
                                !$(".date-range-picker .left-con .date-row .date-td").eq(i).hasClass("gray-date")) {
                                $(".date-range-picker .left-con .date-row .date-td").eq(i).addClass("hover").attr("tabindex", 0).focus();
                                break;
                            }
                        }
                        if ($(".date-range-picker .left-con .date-row .date-td").eq(0).hasClass("empty-date")) {
                            $(".date-td.start-date.active").addClass("hover").attr("tabindex", 0).focus();
                        }
                    }
                    blockingMouse(event);
                    event.stopPropagation();
                });

                // 上个月 事件的绑定
                $(document).on("click", ".preMonth", function () {

                    // event.stopPropagation();

                    if (month > 11) {
                        year = year + 1;
                        month = 0;
                    }

                    if (month < 0) {
                        year = year - 1;
                        month = 11;
                    }

                    month = month - 1;

                    replace(year, month);
                });

                // 下个月 事件的绑定
                $(document).on("click", ".nextMonth", function () {
                    //event.stopPropagation();

                    month = month + 1;
                    if (month > 11) {
                        year = year + 1;
                        month = 0;
                    }
                    if (month < 0) {
                        year = year - 1;
                        month = 11;
                    }

                    replace(year, month);
                });

                $(document).off("click", '.date-td:not(.empty-date, .gray-date)');
                $(document).on("click", '.date-td:not(.empty-date, .gray-date)', function () {
                    let curgroup_length =   $(input_this).closest(".calendar-group").find(".calendar-input").length;
                    let has_disabled = $(input_this.closest(".calendar-group").find(".calendar-input")[1]).hasClass(setting.disableClassName);
                    // console.log(has_disabled)
                    if (curgroup_length === 2 && !has_disabled) { // 双日历（两个日历框，且第二个日历框没有被禁用）

                        // 往返 输入框的val
                        var depart_input = $(input_this.closest(".calendar-group").find(".calendar-input")[0]).val();
                        var return_input = $(input_this.closest(".calendar-group").find(".calendar-input")[1]).val();

                        // 修改判断条件，左右切换的时候 .date-td.start-date .date-td.end-date 可能不在面板上
                        if (depart_input && return_input) {//重新选中往返
                            if (isClick_roundTrip) {// 如果点击的是 返回的日期

                                $('.date-td').removeClass("end-date active");
                                $('.date-td.range').removeClass("range");
                                $('.date-td').removeClass("hover");//键盘事件

                                end_date = "";
                                $(this).addClass("end-date active");
                                end_date = $(this).attr("data-text");

                                // input_this.val(dateFammer(end_date,setting.languageType))
                                input_this.val(end_date);
                                input_this.attr("data-date", end_date);
                                // 消失面板
                                $(".date-range-picker").hide();
                            } else { // 点击去程 给返程加上默认值 给默认值添加选中效果
                                $('.date-td').removeClass("start-date active");
                                $('.date-td').removeClass("end-date active");
                                $('.date-td.range').removeClass("range");
                                $('.date-td').removeClass("hover");//键盘事件

                                start_date = "";
                                $(this).addClass("start-date active");
                                start_date = $(this).attr("data-text");
                                const unchanged = input_this.hasClass('unchanged') || false;
                                input_this.val(start_date);
                                input_this.attr("data-date", start_date);
                                input_this.removeClass("unchanged");
                                // 消失面板
                                $(".date-range-picker").hide();
                                // 第一次点去程 || 如果去程大于回程 则再选一次回程
                                if (unchanged|| new Date(start_date) > new Date(return_input)) {
                                  if(input_this.hasClass("bussiness-depart-input")){
                                    end_date = changeDate(setting.nextDate, start_date);
                                    input_this.closest(".calendar-group").find(".bussiness-return-input").val(end_date);
                                    input_this.closest(".calendar-group").find(".bussiness-return-input").attr("data-date", end_date);
                                    input_this.closest(".calendar-group").find(".bussiness-return-input").focus();
                                  }
                                }
                            }

                        } else if (depart_input && !return_input) {//选中回程
                            // 需要判断结束日期在开始日期之前
                            end_date = $(this).attr("data-text");
                            if (new Date(end_date) < new Date(start_date)) {// 交换日期
                                var temp = end_date;
                                end_date = start_date;
                                start_date = temp;
                                $('.date-td.start-date').removeClass("start-date").addClass("end-date");
                                $(this).addClass("start-date active");
                                // input_this.val(dateFammer(start_date,setting.languageType))
                                input_this.val(start_date);
                                input_this.attr("data-date", end_date);
                            } else {
                                $(this).addClass("end-date active");
                            }
                            //赋值 input
                            $(input_this.closest(".calendar-group").find(".calendar-input")[1]).val(end_date);
                            $(input_this.closest(".calendar-group").find(".calendar-input")[1]).attr("data-date", end_date);
                            // 消失面板
                            $(".date-range-picker").hide();
                        } else {//选中去程
                            start_date = $(this).attr("data-text");
                            $(this).addClass("start-date active");
                            $('.date-td').removeClass("hover");//键盘事件的hover删除
                            //赋值 input
                            // input_this.val(dateFammer(start_date,setting.languageType))//input对象
                            input_this.val(start_date);//input对象
                            input_this.attr("data-date", start_date);//input对象
                        }
                    } else {
                        //event.stopPropagation();
                        start_date = $(this).attr("data-text");
                        $(".date-td").removeClass("start-date active");
                        $(".date-td").removeClass("hover");//处理与键盘事件统一的时候
                        $(this).addClass("start-date active");
                        //赋值 input
                        input_this && input_this.val(start_date);
                        input_this && input_this.attr("data-date", start_date);
                        input_this && input_this.change();// 手动触发change事件
                        // 消失面板
                        $(".date-range-picker").hide();
                    }

                });
                // 加连接hover事件
                $(document).on("mouseover mouseout", '.date-td:not(.empty-date, .gray-date)', function () {
                    // 判断当前点击的是 第几个输入框 0为去程输入框 1为返程输入框
                    var isClick_roundTrip = input_this && input_this.closest(".calendar-group").find(".calendar-input").index(input_this);

                    if (isClick_roundTrip) { // 点击是返程日期框 添加hover效果
                        var now_td = new Date($(this).attr("data-text"));//鼠标当前
                        var start_td = new Date($('.date-td.start-date').attr("data-text"));

                        if ($('.date-td.start-date').length) { // 如果已经有start的
                            $('.date-td').each(function () {
                                var this_td = new Date($(this).attr("data-text"));

                                if ((this_td >= start_td && this_td <= now_td) || (this_td <= start_td && this_td >= now_td)) {
                                    $(this).addClass("range");
                                } else {
                                    $(this).removeClass("range");
                                }

                            });
                        }
                    }
                });

            },

            destroy: function () {
                $('.date-range-picker').remove();

                $(document).off("keydown", ".date-td.hover:not(.empty-date, .gray-date)");
                $(document).off("click mouseover mouseout", ".date-td:not(.empty-date, .gray-date)");
                $dateRangePicker.unbind();
                $dateRangePicker.off("mousedown click keydown");
                // $(".close-picker").off("click keydown");
                $(".date-range-picker").off("scroll");
                $(".preMonth,.nextMonth,.close-picker").off("click keydown");
            }
        };

        // 移动端背景
        function setBgForPhone(month) {
            // 增加移动端背景图
            var rightBgForPhone = month + 1;
            if (rightBgForPhone > 12) rightBgForPhone = 1;

            var bgSize = rightBgForPhone > 9 ? "10rem 8rem" : "5rem 8rem";
            var style = `background: url('../images/temp/${rightBgForPhone}.png') 50% 1rem / ${bgSize} no-repeat;`;

            return style;
        }

        function createDom(year, month) {

            var html = '<div class="date-range-picker" tabindex="0">';
            html += '<div class="picker-wrap">'
            html += '<div class="close-picker-wrap" id="close-picker" tabindex="0"><span class="icon-zh icon-zh-close close-picker"></span></div>';
            html += '<div class="picker-body clearfix"><div class="picker-con left-con">' +
                '<div class="picker-hd"><span class="preMonth icon-left-triangle" tabindex="0"><span class="icon-zh icon-zh-left"></span></span><h3 class="month-t">' + getHead(year, month) + '</h3></div>' +//头部
                '<table class="date-table" role="grid" cellspacing="0" border="0" cellpadding="0">' +
                '<thead><tr class="date-row"><th>' + week_rr[0] + '</th><th>' + week_rr[1] + '</th><th>' + week_rr[2] + '</th><th>' + week_rr[3] + '</th><th>' + week_rr[4] + '</th><th class="red-color">' + week_rr[5] + '</th><th class="red-color">' + week_rr[6] + '</th></tr></thead>' +
                '<tbody class="month-con">' +
                getMonth(year, month) +//月份
                '</tbody>' +
                '</table></div>';

            html += '<div class="picker-con right-con">' +
                '<div class="picker-hd"><h3 class="month-t">' + getHead(year, month + 1) + '</h3><span class="nextMonth icon-right-triangle" tabindex="0"><span class="icon-zh icon-zh-right"></span></span></div>' +
                '<table class="date-table" role="grid" cellspacing="0" border="0" cellpadding="0">' +
                '<thead><tr class="date-row"><th>' + week_rr[0] + '</th><th>' + week_rr[1] + '</th><th>' + week_rr[2] + '</th><th>' + week_rr[3] + '</th><th>' + week_rr[4] + '</th><th class="red-color">' + week_rr[5] + '</th><th class="red-color">' + week_rr[6] + '</th></tr></thead>' +
                '<tbody class="month-con">' +
                getMonth(year, month + 1) +
                '</tbody></table>';


            html += '</div></div></div>';

            return html;
        }

        //计算一个月有多少天
        function daysInMonth(month, year) {
            return new Date(year, month + 1, 0).getDate();
        }

        // 月份头部 日历dome 结构
        function getHead(year, month) {

            if (month > 11) {
                year = year + 1;
                month = 0;
            }

            if (month < 0) {
                year = year - 1;
                month = 11;
            }
            switch (setting.languageType) {
                case 'zn':
                case 'ja':
                    return year + '年 ' + month_rr[month];
                default:
                    return month_rr[month] + " " + year;
            }
        }

        // pre next 对html 进行替换 key_str按键
        function replace(year, month, key_str) {
            var left = $(".date-range-picker .left-con");
            var right = $(".date-range-picker .right-con");

            left.find(".month-t").html(getHead(year, month));
            right.find(".month-t").html(getHead(year, month + 1));

            var leftBg = month + 1;
            if (leftBg === 0) leftBg = 12;
            // 增加背景图
            left.find(".month-con").css({
                "background": `url("../images/temp/${leftBg}.png") no-repeat`,
                "background-size": leftBg > 9 ? "10rem 8rem" : "5rem 8rem",
                "background-position": "50% 1rem",
            });
            var rightBg = month + 2;
            if (rightBg > 12) rightBg = 1;
            right.find(".month-con").css({
                "background": `url("../images/temp/${rightBg}.png") no-repeat`,
                "background-size": rightBg > 9 ? "10rem 8rem" : "5rem 8rem",
                "background-position": "50% 1rem",
            });


            if (key_str) {
                left.find(".month-con").html(getMonth(year, month, key_str));
                right.find(".month-con").html(getMonth(year, month + 1));
            } else {
                left.find(".month-con").html(getMonth(year, month));
                right.find(".month-con").html(getMonth(year, month + 1));
            }
        }

        // 月份的html 结构 key_str 按键情况 日历dome 结构
        function getMonth(year, month, key_str) {

            if (month > 11) {
                year = year + 1;
                month = 0;
            }

            if (month < 0) {
                year = year - 1;
                month = 11;
            }

            // 这个月的第一天 Thu Apr 1 2024 00:00:00 GMT+0800 (中国标准时间)
            var firstDay = new Date(year, month, 1);
            // 这个月有多少天 30
            var dayInMonth = daysInMonth(month, year);
            // 这个月的最后一天 Thu Apr 30 2024 00:00:00 GMT+0800 (中国标准时间)
            var lastDay = new Date(year, month, dayInMonth);

            // 第一天星期几(0-6)
            var weekday = firstDay.getDay() - 1;
            // 最后一天星期几
            var lastDayWeekDay = lastDay.getDay();
            // 每一个月都是从1号开始
            var date = 1;

            // 今年今月今日
            var nowYear = new Date().getFullYear();
            var nowMonth = new Date().getMonth();
            var nowDay = new Date().getDate();

            // html的字符串
            var dayStr = '<tr class="date-row">';

            if (weekday === -1) {
                weekday = 6;
            }
            // 补齐前面的空格
            for (var i = 0; i < weekday; i++) {
                dayStr += '<td class="date-td empty-date"></td>';
            }

            for (; date <= dayInMonth; date++) {

                // html属性
                var title = year + "-" + (month + 1) + "-" + date;
                // 2024-04-02
                var temp_month = month + 1 < 10 ? "0" + (month + 1) : month + 1;
                var temp_day = date < 10 ? "0" + date : date;
                var data_text = year + "-" + temp_month + "-" + temp_day;
                // var data_text = year+"-"+(month+1)+"-"+date;

                //开始时间到结束时间之间 加上连接线 判断条件
                var range = new Date(data_text).getTime() > new Date(start_date).getTime() && new Date(data_text).getTime() < new Date(end_date).getTime();
                //今年今月今日 判断条件
                var now = nowYear === year && nowMonth === month && nowDay === date;
                //周末 判断条件
                var weekend = weekday === 5 || weekday === 6;
                // 判断是否比今日小
                var is_early = (nowYear === year && nowMonth === month && date < nowDay) || year < nowYear || (year === nowYear && month < nowMonth);// 比今天小的都置灰 --add--
                // 开始时间之前的 都要置灰
                var this_td = new Date(data_text);
                var start_td = new Date(start_date);
                var defult_td = new Date(default_start);
                if ((is_early || (this_td < start_td && isClick_roundTrip) || (this_td < defult_td && !isClick_roundTrip)) && datepickerDisabledBefore || this_td < new Date(disableLastDate)) {// 比今天小的都置灰  开始时间之前的 都要置灰 --add--
                    dayStr += '<td id="calendar' + data_text + '" class="date-td gray-date" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                }
                // 左右 按键情况
                else if (key_str === "last" && date === dayInMonth) {// 需要判断是否是周末
                    var classes = weekend ? "date-td hover weekend" : "date-td hover";
                    dayStr += '<td id="calendar' + data_text + '" class="' + classes + '" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else if (key_str === "next" && date === 1) {// 需要判断是否是周末
                    var classes = weekend ? "date-td hover weekend" : "date-td hover";
                    dayStr += '<td id="calendar' + data_text + '" class="' + classes + '" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else if (now) {// 今年今月今日
                    if (start_date === data_text) {
                        dayStr += '<td id="calendar' + data_text + '" class="date-td start-date active" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day day-now">' + date + '</span></td>';
                    } else {
                        dayStr += '<td id="calendar' + data_text + '" class="date-td" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day day-now">' + date + '</span></td>';
                    }
                }

                // 开始和结束是同一天
                else if (data_text === start_date && data_text === end_date && group_length === 2) {
                    dayStr += '<td id="calendar' + data_text + '" class="date-td start-date end-date active range" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else if (data_text === start_date) {// 开始时间选中的开始时间
                    dayStr += '<td id="calendar' + data_text + '" class="date-td start-date active" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else if (data_text === end_date && group_length === 2) {//选中的结束时间
                    dayStr += '<td id="calendar' + data_text + '" class="date-td end-date active range" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else if (range && group_length === 2) {//开始时间到结束时间之间 加上连接线
                    var class_str = "date-td range";
                    // 判断是否是 今天或者周末
                    class_str = now ? (class_str + " today") : class_str;
                    class_str = weekend ? (class_str + " weekend") : class_str;
                    dayStr += '<td id="calendar' + data_text + '" class="' + class_str + '" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else if (weekend) {// 周末
                    dayStr += '<td id="calendar' + data_text + '" class="date-td weekend" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                } else {//平时
                    dayStr += '<td id="calendar' + data_text + '" class="date-td" aria-label="' + title + '" data-text="' + data_text + '" ><span class="day">' + date + '</span></td>';
                }
                weekday++;

                if (weekday % 7 === 0) {//逢7换行
                    weekday = 0;
                    if (date === dayInMonth) { // 最后一天 不需要加上<tr class="date-row">
                        dayStr += '</tr>';
                    } else {
                        dayStr += '</tr>' +
                            '<tr class="date-row">';
                    }
                }
            }

            // 补齐后面的空格
            for (var j = 0; j < (7 - lastDayWeekDay - 1); j++) {
                dayStr += '<td class="date-td empty-date"></td>';
            }

            dayStr += '</tr>';

            return dayStr;
        }

        // 更换月份的结构 key_str 是由按键事件来的
        function changeMonth(year_str, month_str, key_str) {
            if (key_str === "next") {// 按键到最右边或者最后一行的时候 下个月
                month = month_str + 1;
                if (month > 11) {
                    year = year_str + 1;
                    month = 0;
                }
                if (month < 0) {
                    year = year_str - 1;
                    month = 11;
                }
            } else if (key_str === "last") {// 按键到最左边或者第一行的时候 上个月
                month = month_str - 1;

                if (month > 11) {
                    year = year_str + 1;
                    month = 0;
                }
                if (month < 0) {
                    year = year_str - 1;
                    month = 11;
                }

            }
            replace(year, month, key_str);
        }

        // 月份日期上面的按键事件
        function keyboard($object) {

            // 绑定键盘事件
            $(document).on("keydown", ".date-td.hover:not(.empty-date, .gray-date)", function (event) {

                //回车按键
                if (event.keyCode === 13) {// 是否被选中
                    if (group_length === 2 && !has_disabled) {//往返模式
                        if (!input_this.val()) { // 如果这个值为空
                            if (isClick_roundTrip && $(input_this.closest(".calendar-group").find(".calendar-input")[0]).val()) {// 如果点击的是 返回的日期 并且启程有值 --add--
                                $(".date-td.hover").addClass("end-date active");
                                end_date = $(".end-date.active").attr("data-text");
                                // input_this.val(dateFammer(end_date))//
                                input_this.val(end_date);//
                                input_this.attr("data-date", end_date);//

                                $(input_this.closest(".calendar-group").find(".calendar-input")[1]).focus();// 先聚焦 后消失
                                $(".date-range-picker").hide();
                            } else { // old
                                $(".date-td.hover").addClass("start-date active");
                                start_date = $(".start-date.active").attr("data-text");
                                input_this.val(start_date);//
                                input_this.attr("data-date", start_date);//
                            }
                        } else if (!$(input_this.closest(".calendar-group").find(".calendar-input")[1]).val()) {// 返程如果后面的比前面的小，需要进行交换数据
                            end_date = $(this).attr("data-text");
                            if (new Date(end_date) < new Date(start_date)) {// 交换日期
                                var temp = end_date;
                                end_date = start_date;
                                start_date = temp;
                                $('.date-td.start-date').removeClass("start-date").addClass("end-date");
                                $(this).addClass("start-date active");
                                input_this.val(start_date);//
                                input_this.attr("data-date", start_date);//
                            } else {
                                $(this).addClass("end-date active");
                            }
                            //赋值 input
                            $(input_this.closest(".calendar-group").find(".calendar-input")[1]).val(end_date); //
                            $(input_this.closest(".calendar-group").find(".calendar-input")[1]).attr("data-date", end_date); //
                            $(input_this.closest(".calendar-group").find(".calendar-input")[1]).focus();
                            // 消失面板
                            $(".date-range-picker").hide();
                        } else {//input 框都有默认值的时候 重新选中往返  第一次选择
                            $(".start-date.active").removeClass("start-date active");
                            $(".end-date.active").removeClass("end-date active");
                            $(".date-td.range").removeClass("range");//键盘事件
                            if (!isClick_roundTrip) {// focus去程  修改去返程
                                $(".date-td.hover").addClass("start-date active");
                                start_date = $(".start-date.active").attr("data-text");
                                input_this.val(start_date);
                                input_this.attr("data-date", start_date);
                                end_date = changeDate(setting.nextDate, start_date);
                                // 默认设置后面一个输入框的值+3天
                                $(input_this.closest(".calendar-group").find(".calendar-input")[1]).val(end_date);
                                $(input_this.closest(".calendar-group").find(".calendar-input")[1]).attr("data-date", end_date);

                                $(".date-range-picker").hide();
                                $(input_this.closest(".calendar-group").find(".calendar-input")[1]).focus();
                            } else if (isClick_roundTrip) {// focus返程 只修改返程
                                $(".date-td.hover").addClass("end-date active");

                                end_date = $(".end-date.active").attr("data-text");
                                // 默认设置后面一个输入框的值+3天
                                input_this.val(end_date);
                                input_this.attr("data-date", end_date);

                                $(input_this.closest(".calendar-group").find(".calendar-input")[1]).focus();
                                $(".date-range-picker").hide();
                            }
                        }
                    } else {//单程
                        $(".start-date.active").removeClass("start-date active");// 清除上一次的active的状态
                        $(".date-td.hover").addClass("start-date active").removeClass("hover");
                        start_date = $(".start-date.active").attr("data-text");

                        input_this.val(start_date);
                        input_this.attr("data-date", start_date);

                        input_this.focus();//选中之后 回到input
                        $(".date-range-picker").hide();
                    }
                }

                var active = $(".date-td.hover");
                var td_index = active.index();//在当前行在第几个
                var tr_index = active.closest(".date-row").index();//获取当前在第几行
                var now_row = active.closest(".date-row").find(".date-td");// 当前行

                var now_date = now_row.eq(td_index - 1).find(".day").html();//向左 当前选中的天数
                var now_right_date = now_row.eq(td_index + 1).find(".day").html();//向右 当前选中的天数

                var date_row = active.closest(".month-con").find(".date-row");//获取当前月的date-row对象
                var last_row = date_row.eq(tr_index - 1).find(".date-td");//获取上一行的date-td
                var next_row = date_row.eq(tr_index + 1).find(".date-td");//获取下一行的date-td

                // 相差时间
                var now_time, start_time, difDays;

                if (group_length === 1 && !setting.hasDefault) {//单程
                    var temp_month = month + 1 < 10 ? "0" + (month + 1) : month + 1;
                    var start_day = day < 10 ? "0" + day : day;
                    default_start = year + "-" + temp_month + "-" + start_day;
                }

                if (datepickerDisabledBefore) { // 今天之前的需要置灰 获取时间差
                    now_time = Date.parse(new Date($(this).attr("data-text")));
                    start_time = Date.parse(new Date(default_start));
                    difDays = Math.abs(parseInt((now_time - start_time) / 1000 / 3600 / 24));
                }

                //上下左右按键
                if (event.keyCode === 38) {// 键盘上
                    if (tr_index === 0) { //第一行向上
                        if (datepickerDisabledBefore) {// 有默认置灰 差距7天以前的可以变换
                            if (!(difDays < 7)) { //时间差距小于7天的话，不能动
                                changeMonth(year, month, "last");
                                $(".date-td").removeClass("hover");
                                var month_con = $(".left-con .month-con .date-row");
                                if (month_con.eq(month_con.length - 1).find(".date-td").eq(td_index).hasClass("empty-date")) {// 上个月的最后一个行的第n个
                                    month_con.eq(month_con.length - 2).find(".date-td").eq(td_index).addClass("hover");
                                    $(".date-td.hover").attr("tabindex", 0).focus();
                                } else {
                                    month_con.eq(month_con.length - 1).find(".date-td").eq(td_index).addClass("hover");
                                    $(".date-td.hover").attr("tabindex", 0).focus();
                                }
                            }
                        } else {
                            changeMonth(year, month, "last");
                            $(".date-td").removeClass("hover");
                            var month_con = $(".left-con .month-con .date-row");
                            if (month_con.eq(month_con.length - 1).find(".date-td").eq(td_index).hasClass("empty-date")) {// 上个月的最后一个行的第n个
                                month_con.eq(month_con.length - 2).find(".date-td").eq(td_index).addClass("hover");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            } else {
                                month_con.eq(month_con.length - 1).find(".date-td").eq(td_index).addClass("hover");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }
                        }

                    } else {
                        if (last_row.eq(td_index).hasClass("empty-date")) {//向上一行 有空元素
                            if (datepickerDisabledBefore) {
                                if (!(difDays < 7)) {// 有默认置灰 时间差距小于7天的话，不能动
                                    changeMonth(year, month, "last");
                                    $(".date-td").removeClass("hover");
                                    var month_con = $(".left-con .month-con .date-row");
                                    month_con.eq(month_con.length - 1).find(".date-td").eq(td_index).addClass("hover");
                                    $(".date-td.hover").attr("tabindex", 0).focus();
                                }
                            } else {
                                changeMonth(year, month, "last");
                                $(".date-td").removeClass("hover");
                                var month_con = $(".left-con .month-con .date-row");
                                month_con.eq(month_con.length - 1).find(".date-td").eq(td_index).addClass("hover");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }

                        } else {
                            if (!last_row.eq(td_index).hasClass("gray-date")) {
                                $(".date-td.hover").removeAttr("tabindex");
                                $(".date-td").removeClass("hover");
                                last_row.eq(td_index).addClass("hover");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }
                        }
                    }
                    return false;
                }
                if (event.keyCode === 40) {// 键盘下
                    if (tr_index === date_row.length - 1) {//最后一行 换到下个月
                        changeMonth(year, month, "next");
                        $(".date-td").removeClass("hover");
                        var month_con = $(".left-con .month-con .date-row");
                        if (month_con.eq(0).find(".date-td").eq(td_index).hasClass("empty-date")) {// 下个月的第一行有空的话 就换成第二行
                            month_con.eq(1).find(".date-td").eq(td_index).addClass("hover");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        } else {
                            month_con.eq(0).find(".date-td").eq(td_index).addClass("hover");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        }
                    } else {
                        if (next_row.eq(td_index).hasClass("empty-date")) { // 最后一行第n个是空元素 换到下个月第一行的第n个
                            changeMonth(year, month, "next");
                            $(".date-td.hover").removeAttr("tabindex");
                            $(".date-td").removeClass("hover");
                            var month_con = $(".left-con .month-con .date-row");
                            month_con.eq(0).find(".date-td").eq(td_index).addClass("hover");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        } else {
                            $(".date-td.hover").removeAttr("tabindex");
                            $(".date-td").removeClass("hover");
                            next_row.eq(td_index).addClass("hover");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        }
                    }
                    return false;
                }
                if (event.keyCode === 37) {// 键盘左
                    var left;
                    if (td_index === 0) {//选中上一行的最后一个
                        if (tr_index === 0) {//第一行
                            if (!($(".date-td.hover").attr("data-text") === default_start && datepickerDisabledBefore)) { // 当月的第一天 前面的都是灰色
                                changeMonth(year, month, "last");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }
                        } else {
                            if (!last_row.eq(last_row.length - 1).hasClass("gray-date")) { // 前一个不是灰色
                                $(".date-td.hover").removeAttr("tabindex");
                                $(".date-td").removeClass("hover");
                                last_row.eq(last_row.length - 1).addClass("hover");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }
                        }
                    } else { // 同一行的操作
                        if (now_date === undefined) {//左边为空的时候
                            if (!($(".date-td.hover").attr("data-text") === default_start && datepickerDisabledBefore)) { // 当月的第一天 前面的都是灰色
                                changeMonth(year, month, "last");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }
                        } else {
                            if (!now_row.eq(td_index - 1).hasClass("gray-date")) { // 前一个不是灰色
                                left = td_index - 1;
                                $(".date-td.hover").removeAttr("tabindex");
                                now_row.eq(left).addClass("hover").siblings(".date-td").removeClass("hover");
                                $(".date-td.hover").attr("tabindex", 0).focus();
                            }
                        }
                    }
                    return false;
                }
                if (event.keyCode === 39) {//键盘右
                    var right;
                    if (td_index === 6) {//选中下一行的第一个
                        if (tr_index === date_row.length - 1) {//最后一行 翻页
                            changeMonth(year, month, "next");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        } else {
                            $(".date-td.hover").removeAttr("tabindex");
                            $(".date-td").removeClass("hover");
                            next_row.eq(0).addClass("hover");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        }
                    } else { // 同一行的操作
                        if (now_right_date === undefined) { //右边为空的时候
                            changeMonth(year, month, "next");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        } else {
                            right = td_index + 1;
                            $(".date-td.hover").removeAttr("tabindex");
                            now_row.eq(right).addClass("hover").siblings(".date-td").removeClass("hover");
                            $(".date-td.hover").attr("tabindex", 0).focus();
                        }
                    }
                    return false;
                }
            });
        }

        // input框的focus事件的绑定
        function bindFocus($object) {
            $object.bind("focus", function (event) {

                // 全局的赋值
                input_this = $(this);// 赋值 this对象
                // 当前点击的是单日历还是双日历框 1为单日历 2为双日历
                group_length = input_this.closest(".calendar-group").find(".calendar-input").length;
                // 判断当前点击的是去程还是返程 0为去程输入框 1为返程输入框
                isClick_roundTrip = input_this.closest(".calendar-group").find(".calendar-input").index(input_this);
                // 返程的日历框是否禁用
                if (group_length === 2) {
                    has_disabled = $(input_this.closest(".calendar-group").find(".calendar-input")[1]).hasClass(setting.disableClassName);
                }

                // 展示面板处理方法
                showDatePicker(event, this);

                if (setting.hasDefault && setting.bindFocus) {
                    setting.bindFocus = false;
                    keyboard($(".date-td.hover"));//绑定键盘事件
                }
                // 一个日历插件
                if ($(".start-date.active").attr("data-text") && $(".end-date.active").attr("data-text")) {//往返
                    $(".date-td.hover").removeClass("hover");// 清除所有的hover 状态
                    if (!isClick_roundTrip) {// 去程
                        $(".start-date.active").addClass("hover").attr("tabindex", 0);
                    } else if (isClick_roundTrip) { //返程
                        $(".end-date.active").addClass("hover").attr("tabindex", 0);
                    }
                } else if ($(".start-date.active").attr("data-text")) {// 已经选中一次
                    $(".date-td.hover").removeClass("hover");
                    $(".start-date.active").addClass("hover").attr("tabindex", 0);
                } else if ($(".date-td.today") && !$(".date-td.hover").attr("data-text") && !$(this).val()) {//第一次进来默认选中今天
                    $(".date-td.today").addClass("hover").attr("tabindex", 0);
                    keyboard($(".date-td.hover"));//绑定键盘事件
                }

                // 调用公共处理方法 this为input对象
                focusAndClick(this);

                // input_this = $(this);// 赋值 this对象

            });
        }

        // focus和click 面板处理的相同方法 _this 为input的对象
        function focusAndClick(_this) {
          if($(_this).hasClass('calendar-input-disabled-before')){
            // 今日之前的日期：可以选择
            datepickerDisabledBefore = false;
          }
          else{
            // 今天之前的日期：禁用
            datepickerDisabledBefore = true;
          }
          // 这个日期前的日期都要禁用
          disableLastDate = $(_this).attr("data-disable-last-date")

          input_this = $(_this);
            if (isMobile) {
                replace(year, month);
            }
            // 非移动端才进行赋值，移动端因为滚动，不用重新赋值
            if (!isMobile()) {
                // 重新赋值 year month day
                year = new Date($(_this).attr("data-date")).getFullYear();
                month = new Date($(_this).attr("data-date")).getMonth();
                day = new Date($(_this).attr("data-date")).getDate();
            }

            // 在replace之前，先start-date和end-date 需要改变
            if (group_length === 2 && !has_disabled) {//往返 且没有被禁止
                if (isClick_roundTrip) {// 如果点击的是 返回的日期 --add--
                    end_date = $(_this).attr("data-date");
                    start_date = $(_this).closest(".calendar-group").find(setting.className).attr("data-date");
                    var start_date_year = new Date(start_date).getFullYear();
                    var start_date_mouth = new Date(start_date).getMonth();
                    if (start_date_year === year &&
                        (month - start_date_mouth === 1 || month - start_date_mouth === 0) && (!isMobile() || window.document.body.offsetWidth > 767)) {// 如果两个面板相差不超过一个月，面板以去程时间为准
                        // 更新面板
                        replace(start_date_year, start_date_mouth);
                    } else if ((year - start_date_year === 1) && (month - start_date_mouth === 1 || month - start_date_mouth === 0) && (!isMobile() || window.document.body.offsetWidth > 767)) {
                        // 更新面板
                        replace(start_date_year, start_date_mouth);
                    } else if ((!isMobile() || window.document.body.offsetWidth > 767)) {// 其余情况 按返程时间为准
                        replace(new Date($(_this).attr("data-date")).getFullYear(), new Date($(_this).attr("data-date")).getMonth());
                    }
                } else { // 去程日期
                    start_date = $(_this).attr("data-date");
                    end_date = $($(_this).closest(".calendar-group").find(".calendar-input")[1]).attr("data-date");
                    if ((!isMobile() || window.document.body.offsetWidth > 767)) {
                        // 更新面板
                        replace(new Date($(_this).attr("data-date")).getFullYear(), new Date($(_this).attr("data-date")).getMonth());
                    }
                }
            } else {//单程
                start_date = $(_this).attr("data-date");//更新start_date的赋值
                end_date = undefined;//更新start_date的赋值
                if ((!isMobile() || window.document.body.offsetWidth > 767)) {
                    // 更新面板
                    replace(new Date($(_this).attr("data-date")).getFullYear(), new Date($(_this).attr("data-date")).getMonth());
                }
            }

            if (group_length === 2 && !has_disabled) {//往返 且没有被禁止
                if (isClick_roundTrip) {// 如果点击的是 返回的日期 --add--
                    $(".date-td").each(function () { //把找出与end_date相等的加上 hover 和tabindex
                        var now_date = $(this).attr("data-text");
                        if (now_date === end_date) {
                            $(this).addClass("hover").attr("tabindex", 0);
                            return false;
                        }
                        if (now_date === start_date) {
                            $(this).addClass("range");
                        }
                    });
                } else { // 去程日期
                    $(".date-td").each(function () { //把找出与start_date相等的加上 hover 和tabindex
                        var now_date = $(this).attr("data-text");
                        var this_td = new Date(now_date);
                        if (this_td.getTime() === new Date(start_date).getTime()) {
                            $(this).addClass("hover range").attr("tabindex", 0);
                            return false;
                        }
                    });
                }
                if (!$(_this).val() && !isClick_roundTrip) {//反程 第二次为空的情况
                    $(".start-date.active").removeClass("start-date active hover");
                    $(".end-date.active").removeClass("end-date active hover");
                    $(".date-td.range").removeClass("range");
                    $(".date-td.today").addClass("hover").attr("tabindex", 0);
                } else if (start_date !== $(".start-date.active").attr("data-text") || end_date !== $(".end-date.active").attr("data-text")) {//第二次不为空 重新赋值
                    $(".date-td.range").removeClass("range");
                    $(".start-date.active").removeClass("start-date active");
                    $(".end-date.active").removeClass("end-date active");
                    $(".date-td.hover").removeClass("hover");// 清空所有的hover状态

                    $(".date-td").each(function () { //重新连接起来
                        var now_date = $(this).attr("data-text");
                        var this_td = new Date(now_date);
                        var start_td = new Date(start_date);
                        var end_td = new Date(end_date);
                        if (now_date === start_date) {
                            $(this).addClass("start-date active range hover").attr("tabindex", 0);
                        } else if (now_date === end_date) {
                            $(this).addClass("end-date active range");
                            return false;
                        } else if (this_td > start_td && this_td < end_td) {// 开始结束之间的
                            $(this).addClass("range");
                        }
                    });
                }
            } else {//单程
                // 删除 返程选中的样式以及 hover样式
                $(".end-date.active").removeClass("end-date active");
                $(".range").removeClass("range");
                $(".start-date.active").removeClass("start-date active hover").removeAttr("tabindex");

                if (!start_date) {//单程 第二次为空的情况
                  $(".date-td.today").addClass("hover").attr("tabindex", 0);
                } else if (start_date !== $(".start-date.active").attr("data-text")) {//第二次不为空 重新赋值
                    // $(".start-date.active").removeClass("start-date active").removeAttr("tabindex");

                    $(".date-td").each(function () {
                        if ($(this).attr("data-text") === start_date && $(input_this).val() === start_date) {
                            $(".start-date.active").removeClass("start-date active").removeAttr("tabindex");
                            $(this).addClass("start-date active hover").attr("tabindex", 0);
                            return false;
                        }
                    });
                } else if (start_date === $(".start-date.active").attr("data-text")) { //相等的时候 进行hover 赋值
                    $(".start-date.active").addClass("hover").attr("tabindex", 0);
                }
            }
        }

        // focus和click show面板相同方法 _this 为input的对象
        function showDatePicker(event, _this) {
            var left, right;
            if (($(_this).offset().left + datePickerWidth) > $(window).width()) {// datePickerWidth 面板宽度
                left = "inherit";
                right = ($(window).width() - $(_this).offset().left - $(_this).closest(".input-group").width() - 20);

            } else {
                left = $(_this).offset().left -20;
                right = "inherit";
            }
            var dateFormat = /^(\d{4})-(\d{2})-(\d{2})$/;
            // 刷新面板
            if (_this.value && dateFormat.test(_this.value) && !isMobile()) {// 手机端的时候 不进行重新刷新
                var nowDate = new Date(_this.value);
                year = nowDate.getFullYear();
                month = nowDate.getMonth();
                var now_title = month_rr[month] + " " + year;
                var panel_title = $(".picker-con.left-con").find(".month-t").html();
                // 面板不等于 input里面的 才会刷新
                if (now_title !== panel_title) {// 左右侧的面板 相差1的时候 可以不刷新面板
                    if (!(month_rr[month - 1] + " " + year === panel_title)) {
                        replace(year, month);
                    }
                }
            }

            event.stopPropagation();
            $(".date-range-picker").css(
                {
                    "left": left,
                    "right": right,
                    "top": $(_this).offset().top + $(_this).outerHeight(),
                    "display": "block",
                    "z-index": 1000
                });
        }

        // 阻止键盘操作公共方法 滚动 滚动条
        function blockingMouse(event) {
            if (event.keyCode === 38) {// 键盘上
                return false;
            }
            if (event.keyCode === 40) {// 键盘下
                return false;
            }
            if (event.keyCode === 37) {// 键盘左
                return false;
            }
            if (event.keyCode === 39) {//键盘右
                return false;
            }
        }

        // 默认修改时间，根据时间返回多少天之后的date
        function changeDate(p_count, orgdate) {
            var date = new Date();
            if (orgdate) {
                date = new Date(orgdate);
            }
            date.setDate(date.getDate() + p_count);//获取p_count天后的日期
            var yy = date.getFullYear();
            var mm = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
            var dd = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
            var dateStr = yy + "-" + mm + "-" + dd;
            return dateStr;
        }

        // 判断是不是手机端
        function isMobile() {
            var userAgentInfo = navigator.userAgent;

            var mobileAgents = ["Android", "iPhone"];

            var mobile_flag = false;

            //根据userAgent判断是否是手机
            for (var v = 0; v < mobileAgents.length; v++) {
                if (userAgentInfo.indexOf(mobileAgents[v]) > 0) {
                    mobile_flag = true;
                    break;
                }
            }

            var screen_width = window.document.body.offsetWidth;

            //根据屏幕宽度判断是否是手机
            if (screen_width < 768) {
                mobile_flag = true;
            }

            return mobile_flag;
        }

        dateRangePicker.init();

        return dateRangePicker;
    };
}(jQuery);
