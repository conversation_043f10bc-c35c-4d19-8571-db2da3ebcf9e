// datepicker 组件
@import "../../less/variables";

.date-range-picker {
  display: none;
  position: absolute;
  padding: 0.75rem 1rem 1rem;
  background-color: #fff;
  box-shadow: 1px 1px 0.6rem 0 rgba(0, 0, 0, 0.2);
  border-radius: 5px;

  .picker-wrap {
    position: relative;
  }

  .close-picker-wrap {
    border-radius: 5px 5px 0 0;
    margin: -0.75rem -1rem -1rem;
    text-align: right;
    padding-right: 0.9rem;
    line-height: 2.5rem;
    background: linear-gradient(180deg, #fdfdfd 0%, #fafafa 100%);

    .close-picker {
      cursor: pointer;
      color: #757575;
      font-size: 0.6rem;
    }
  }

  .preMonth {
    float: left;
    cursor: pointer;
    .icon-zh {
      font-size: 18px;
      color: #4f3d1e;
    }
  }

  .nextMonth {
    float: right;
    cursor: pointer;
    .icon-zh {
      font-size: 18px;
      color: #4f3d1e;
    }
  }

  .picker-body {
    margin-top: 1.75rem;
  }

  .picker-hd {
    text-align: center;
    margin-bottom: 1rem;
    //position: relative;
    h3 {
      display: inline-block;
      margin-bottom: 0;
      font-size: 1rem;
      line-height: 1.2rem;
      color: @gray-101010;
      cursor: default;
    }
  }
  .date-row {
    color: @gray-dark;
    th {
      display: inline-block;
      width: 2.2rem;
      height: 1.1rem;
      line-height: 1.1rem;
      margin-bottom: 1rem;
      text-align: center;
      cursor: default;
      color: @gray-75;
      font-size: 0.7rem;
    }
    .date-td {
      display: inline-block;
      position: relative;
      width: 2.2rem;
      height: 2.2rem;
      font-weight: normal;
      cursor: pointer;
      font-size: 0.8rem;
      &:hover {
        border: 1px solid @picker-color-010;
        border-radius: 4px;
        background-clip: padding-box;
        color: @picker-color-010;
      }
      &.empty-date {
        &:hover {
          border: none;
          cursor: default;
        }
      }
      &.gray-date {
        color: @gray-d74;
        &:hover {
          border: none;
          cursor: default;
          color: @gray-d74;
        }
      }
      &.weekend {
        color: @picker-color-010;
      }
      &.today {
        color: @picker-color-010;
      }
      &.hover {
        border: 1px solid @picker-color-010;
        border-radius: 4px;
        color: @picker-color-010;
      }
      &.active {
        border-radius: 4px;
        background-color: @picker-bg-color;
        color: #fff;
      }
      &.range {
        background-color: @note-color;
        &.active {
          background-color: @picker-bg-color;
          color: #fff;
        }
        &.today {
          color: @gray-dark;
        }
      }
      .day {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        //transform: translate(-50%, -50%)\0;
        &-now {
          &:before {
            position: absolute;
            content: "";
            bottom: -0.25rem;
            left: 0.25rem;
            width: 4px;
            height: 4px;
            border-radius: 2px;
            background-color: @picker-bg-color;
          }
        }
      }
    }
    .empty-date {
      display: inline-block;
      width: 2.2rem;
      height: 2.2rem;
    }
    .start-date {
      &.range:not(.end-date):after {
        content: "";
        background-color: #fff1f0;
        width: 50%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 50%;
        z-index: -1;
      }
      &.end-date {
        border: 1px solid @picker-bg-color;
        .day {
          display: block;
          width: 1.8rem;
          height: 1.8rem;
          line-height: 1.8rem;
          border-radius: 4px;
          text-align: center;
          background-color: @picker-bg-color;
        }
        &.active {
          background-color: @color-white;
        }
      }
    }
    .end-date {
      &.range:not(.start-date):before {
        content: "";
        background-color: #fff1f0;
        width: 50%;
        height: 100%;
        position: absolute;
        top: 0;
        right: 50%;
        z-index: -1;
      }
    }
    .red-color {
      color: #cc0100;
    }
  }
  .picker-con {
    float: left;
    &.mobile-month {
      display: none;
    }
  }
  .left-con {
    margin-right: 1.5rem;
  }

  @media screen and (max-width: @screen-xs-max) {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    left: 0 !important;
    .picker-con {
      width: 100%;
      .date-table {
        margin: 0 auto;
      }
      &.mobile-month {
        display: block;
      }
    }
    .preMonth,
    .nextMonth {
      display: none;
    }
    .close-picker-wrap {
      position: absolute;
      width: 100%;
      z-index: 99;
      margin: -0.75rem 0 0;
      line-height: 2rem;
    }
    .picker-body {
      margin-top: -0.25rem;
      min-width: 16rem;
      max-height: 27.3rem;
      overflow-y: auto;
    }
    .picker-hd {
      width: 15.4rem;
      margin: 1.5rem auto 1rem auto;
    }
    .left-con {
      margin: 0;
    }
  }
}
