$.fn.slide = function (options) {

    //默认参数、属性
    var defaults = {
        slideCon: '.slide-content',
        slideNavLi: '.slide-nav li',
        slideItem: '.slide-item',
        slideLink: '.link-slide',
        slideTitle: '.link-title',
        slideTab: '.link-tab',
        prev: '.prev',
        next: '.next',
        playBtn: ".play-btn",
        margin: '10', //多列间距
        col: 1,  //展示个数，默认为1
        time: 5000,  //轮播图切换时间
        showIndex: false,  //是否显示索引
        autoPlay: false,  //是否自动播放
        onlyImg: false, //是否只有banner图片
    };

    //options合并到defaults上将所有的赋值给options
    var opt = $.extend(defaults, options);
    var slideEle = $(this);
    var slideContent = slideEle.find(opt.slideCon);
    var index = 0; //当前索引值
    var timer = null;   //定时器
    var slideWidth = ((slideEle.width() - opt.margin * (opt.col - 1)) / opt.col).toFixed(2);
    var oldLength = slideEle.find(opt.slideItem).length;    //item初始长度
    var length = oldLength * 2;   //item复制后的长度
    var loop = 0;
    var autoloop = 0;
    var isAutoloop = false;

    init();

    $(window).resize(function () {
        throttle(reset(), 1000);
    });

    //初始化
    function init() {
        // var wrap = (1920 - $(window).width())/2; // 1440-1920 显示效果
        var left;
        index = oldLength;
        
            left = -slideWidth * index - opt.margin * index;
            $(slideEle.find(opt.slideItem)).css({width: slideWidth, marginRight: opt.margin});
        
        slideContent.append(slideContent.html()).css({
            // width: slideWidth * length + opt.margin * length,
            left: left
        });

        changeTabindex();

        //鼠标悬浮事件
        slideEle.hover(function () {  //移除定时任务
            clearInterval(timer);
            timer = null;
        }, function () {   //添加定时任务
            if (opt.autoPlay) {
                setTimer();
            }
        });

        //按钮点击事件
        slideEle.find(opt.prev).click(function () {
            if (!slideContent.is(':animated')) {
                index--;
                change();
            }

        }).end()
            .find(opt.next).click(function () {
            if (!slideContent.is(':animated')) {
                index++;
                change();
            }
        });

        //导航点击事件委托
        if (opt.showIndex) {
            $(opt.slideNavLi).click(function (event) {
                index = $(event.target).index() + oldLength;
                change();
            });
        }

        if (opt.autoPlay) {
            setTimer();
        }

        //Tab onfocus事件
        slideEle.focus(function () {
            clearInterval(timer);
            timer = null;
            if (opt.autoPlay) {
                isAutoloop = true;
            }

        });

        slideEle.find(opt.slideLink).blur(function () {
            GoTo();
        });

        slideEle.find(opt.playBtn).on("click", function () {
            $(this).toggleClass("on");
            if (opt.autoPlay) {
                opt.autoPlay = false;
                clearInterval(timer);
                timer = null;
            } else {
                setTimeout(function () {
                    index++;
                    change();
                }, 50);
                opt.autoPlay = true;
            }
        });

    }

    //设置定时器
    function setTimer() {

        clearInterval(timer);
        timer = null;

        timer = setInterval(function () {
            index++;
            change();
        }, opt.time);
    }

    function change() {
        changeSlide();
        if (opt.showIndex) {
            changeNav();
        }
    }

    //轮播图切换
    function changeSlide() {

        
        
            slideContent.stop(true).animate({left: -slideWidth * index - opt.margin * index}, 600, function () {
                var col = opt.col;
                if (index <= 0) {
                    index = oldLength;
                }
                if (index >= length - col) {
                    index = oldLength - col;
                }
    
                changeTabindex();
    
                if ($(slideEle).find(".focus-visible").length > 0 && (!opt.onlyImg)) {
                    slideContent.find(opt.slideTitle).eq(index).focus();
                }
    
                if ($(slideEle).find(".focus-visible").length > 0 && opt.onlyImg) {
                    slideContent.find(opt.slideLink).eq(index).focus();
                }
    
                slideContent.css({left: -slideWidth * index - opt.margin * index});
    
            });
        
    }

    //导航点切换
    function changeNav() {
        $(slideEle).find(opt.slideNavLi).removeClass('active').eq(index % oldLength).addClass('active');
    }

    function GoTo() {
        if (isAutoloop) {
            autoloop++;
            if (autoloop === oldLength) {
                slideEle.find(opt.slideItem).each(function () {
                    var link = $(this).find(opt.slideLink);
                    var title = $(this).find(opt.slideTitle);
                    var tab = $(this).find(opt.slideTab);
                    if (link.length > 0 || title.length > 0) {
                        link.attr("tabindex", "-1");
                        title.attr("tabindex", "-1");
                        tab.attr("tabindex", "-1");
                    }
                });
            } else {
                index++;
                change();
            }
        } else {
            loop++;
            if (loop === oldLength) {
                slideEle.find(opt.slideItem).each(function () {
                    var link = $(this).find(opt.slideLink);
                    var title = $(this).find(opt.slideTitle);
                    var tab = $(this).find(opt.slideTab);
                    if (link.length > 0 || title.length > 0) {
                        link.attr("tabindex", "-1");
                        title.attr("tabindex", "-1");
                        tab.attr("tabindex", "-1");
                    }
                });
                loop = 0;

            } else {
                clearInterval(timer);
                timer = null;
                index++;
                change();
            }
        }
    }

    function changeTabindex() {
        var next_index = index;
        next_index++;

        slideEle.find(opt.slideItem).each(function () {
            var temp_index = $(this).index();
            var link = $(this).find(opt.slideLink);
            var title = $(this).find(opt.slideTitle);
            var tab = $(this).find(opt.slideTab);
            if (link.length > 0 || title.length > 0) {
                if (temp_index === index || temp_index === next_index) {
                    link.attr("tabindex", "0");
                    title.attr("tabindex", "0");
                    tab.attr("tabindex", "0");
                } else {
                    link.attr("tabindex", "-1");
                    title.attr("tabindex", "-1");
                    tab.attr("tabindex", "-1");
                }
            }

        });


    }

    //重新设置轮播图片大小
    function reset() {
        var resetWidth = slideEle.width();
        // var wrap = (1920 - $(window).width())/2; // 1440-1920 显示效果
        var left;
        slideWidth = ((resetWidth - opt.margin * (opt.col - 1)) / opt.col).toFixed(2);
            left = -slideWidth * index - opt.margin * index;
            $(slideEle.find(opt.slideItem)).css({width: slideWidth, marginRight: opt.margin});
        
        slideContent.css({
            // width: slideWidth * length + opt.margin * length,
            left: left
        });
    }

    function throttle(fn, delay) {
        var runFlag = false;
        return function (e) {
            // 判断之前的调用是否完成
            if (runFlag) {
                return false;
            }
            runFlag = true;
            setTimeout(function () {
                fn(e);
                runFlag = false;
            }, delay);
        };
    }

};
