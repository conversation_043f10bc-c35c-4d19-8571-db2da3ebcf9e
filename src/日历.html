<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <title>Title</title>
    <link href="./css/common.css" rel="stylesheet" />
</head>
<body>
<div class="container">
    <div>单日历</div>
    <div class="col-lg-4 calendar-group col-xs-12">
        <div class="input-group input-single mB20">
            <div class="label-wrap"><label for="single-date" class="title">date</label></div>
            <input type="text" class="text calendar-input depart-input calendar-flag" id="single-date" placeholder="2024-05-01" aria-required="true" aria-label="calendar，please press enter to choose date">
            <i class="icon-calendar icon calendar"></i>
        </div>
    </div>
    <div>双日历</div>
    <div class="calendar-group">
        <div class="col-lg-4 col-xs-12">
            <div class="input-group input-single mB20">
                <div class="label-wrap">
                    <label for="single-date" class="title">date</label>
                </div>
                <input type="text" class="text calendar-input depart-input calendar-flag" id="single-date1" placeholder="2024-05-01" aria-required="true" aria-label="calendar，please press enter to choose date">
                <i class="icon-calendar icon calendar"></i>
            </div>
        </div>
        <div class="col-lg-4 col-xs-12">
            <div class="input-group input-single mB20">
                <div class="label-wrap"><label for="single-date" class="title">date</label></div>
                <input type="text" class="text calendar-input return-input calendar-flag" id="single-date2" placeholder="2024-05-01" aria-required="true" aria-label="calendar，please press enter to choose date">
                <i class="icon-calendar icon calendar"></i>
            </div>
        </div>
    </div>
</div>

<script>window.jQuery || document.write('<script src="plugins/jquery-3.7.1.min.js"><\/script>')</script>
<script src="components/slide/slide.js"></script>
<script src="js/script.js"></script>
<script>
    $(document).ready(function () {
        $('.calendar-input').dateRangePicker({
            disableClassName: 'disable',
        })
    });
</script>
</body>
</html>