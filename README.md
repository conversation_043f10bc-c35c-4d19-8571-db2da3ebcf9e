# 深圳航空新版

全量版本

## Status

## Branch

- master 主分支，项目稳定发布的产物。
- dev 为开发分支

## Tags

- 格式：版本号
- 示例：v1.0.0

## Contact

- 线上留言在[issue](https://gitlab.cae.com/zjjing/zjjing/issues)

## Develope

开发流程说明

### Project

```
|- src 源码文件夹
    |- modules      业务组件
    |- data         模拟数据
    |- fonts        icon字体
    |- js
    |- plugins      三方插件
    |- less         样式源码
|- build            编译后的监控文件夹
|- dist              最终交付文件夹
|- config.js        关联地址配置
|- gulpfile.js      gulp自动化配置
|- README.md
|- package.json
```

### Ready for develope

#### 1. 安装依赖

```
npm i
//or yarn install
```

#### 2. 执行自动化构建流

```
npm run build
```

```
npm run default
```
